﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AirMonitor.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AM_Devices",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    DeviceAddress = table.Column<byte>(type: "INTEGER", nullable: false, comment: "RS485设备地址"),
                    DeviceType = table.Column<int>(type: "INTEGER", nullable: false, comment: "设备类型"),
                    SerialNumber = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true, comment: "设备序列号"),
                    ModelNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false, comment: "设备型号"),
                    FirmwareVersion = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false, comment: "固件版本"),
                    Status = table.Column<int>(type: "INTEGER", nullable: false, comment: "设备状态"),
                    LastCommunication = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    IsOnline = table.Column<bool>(type: "INTEGER", nullable: false),
                    Location = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true, comment: "设备位置"),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true, comment: "设备描述"),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AM_Devices", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AM_ParameterTemplates",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false, comment: "模板名称"),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false, comment: "模板描述"),
                    ApplicableDeviceType = table.Column<int>(type: "INTEGER", nullable: false, comment: "适用的设备类型"),
                    ModelPattern = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false, comment: "设备型号匹配模式"),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AM_ParameterTemplates", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AM_Users",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Username = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false, comment: "用户名"),
                    Email = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false, comment: "邮箱"),
                    PasswordHash = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false, comment: "密码哈希"),
                    Role = table.Column<int>(type: "INTEGER", nullable: false, comment: "用户角色"),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    LastLoginAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AM_Users", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AM_CommunicationLogs",
                columns: table => new
                {
                    Id = table.Column<long>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    DeviceId = table.Column<int>(type: "INTEGER", nullable: false),
                    CommunicationType = table.Column<int>(type: "INTEGER", nullable: false, comment: "通信类型"),
                    ProtocolFormat = table.Column<int>(type: "INTEGER", nullable: false, comment: "协议格式"),
                    SourceAddress = table.Column<byte>(type: "INTEGER", nullable: false, comment: "源地址"),
                    TargetAddress = table.Column<byte>(type: "INTEGER", nullable: false, comment: "目标地址"),
                    CommandCode = table.Column<byte>(type: "INTEGER", nullable: false, comment: "命令码"),
                    RawData = table.Column<byte[]>(type: "BLOB", nullable: false, comment: "原始数据"),
                    ParsedData = table.Column<string>(type: "TEXT", nullable: true, comment: "解析后的数据JSON"),
                    IsSuccessful = table.Column<bool>(type: "INTEGER", nullable: false),
                    ErrorMessage = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true, comment: "错误消息"),
                    Timestamp = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AM_CommunicationLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AM_CommunicationLogs_AM_Devices_DeviceId",
                        column: x => x.DeviceId,
                        principalTable: "AM_Devices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AM_ParameterDefinitions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ParameterTemplateId = table.Column<int>(type: "INTEGER", nullable: false),
                    CommandCode = table.Column<byte>(type: "INTEGER", nullable: false, comment: "关联的命令码"),
                    ParameterIndex = table.Column<byte>(type: "INTEGER", nullable: false, comment: "参数索引"),
                    Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false, comment: "参数名称"),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false, comment: "参数描述"),
                    Category = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false, comment: "参数类别"),
                    DataType = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false, comment: "数据类型"),
                    ValueFormat = table.Column<int>(type: "INTEGER", nullable: false, comment: "参数值格式"),
                    FormatConfiguration = table.Column<string>(type: "TEXT", nullable: true, comment: "格式配置JSON"),
                    Unit = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false, comment: "参数单位"),
                    MinValue = table.Column<decimal>(type: "TEXT", precision: 18, scale: 6, nullable: true, comment: "最小值"),
                    MaxValue = table.Column<decimal>(type: "TEXT", precision: 18, scale: 6, nullable: true, comment: "最大值"),
                    IsReadOnly = table.Column<bool>(type: "INTEGER", nullable: false),
                    DisplayOrder = table.Column<int>(type: "INTEGER", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AM_ParameterDefinitions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AM_ParameterDefinitions_AM_ParameterTemplates_ParameterTemplateId",
                        column: x => x.ParameterTemplateId,
                        principalTable: "AM_ParameterTemplates",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AM_UserDevicePermissions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    UserId = table.Column<int>(type: "INTEGER", nullable: false),
                    DeviceId = table.Column<int>(type: "INTEGER", nullable: false),
                    PermissionLevel = table.Column<int>(type: "INTEGER", nullable: false, comment: "权限级别"),
                    GrantedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    ExpiresAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AM_UserDevicePermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AM_UserDevicePermissions_AM_Devices_DeviceId",
                        column: x => x.DeviceId,
                        principalTable: "AM_Devices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AM_UserDevicePermissions_AM_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "AM_Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AM_DeviceParameters",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    DeviceId = table.Column<int>(type: "INTEGER", nullable: false),
                    ParameterDefinitionId = table.Column<int>(type: "INTEGER", nullable: false),
                    IsEnabled = table.Column<bool>(type: "INTEGER", nullable: false),
                    CustomName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true, comment: "自定义名称"),
                    CustomConfiguration = table.Column<string>(type: "TEXT", nullable: true, comment: "自定义配置JSON"),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AM_DeviceParameters", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AM_DeviceParameters_AM_Devices_DeviceId",
                        column: x => x.DeviceId,
                        principalTable: "AM_Devices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AM_DeviceParameters_AM_ParameterDefinitions_ParameterDefinitionId",
                        column: x => x.ParameterDefinitionId,
                        principalTable: "AM_ParameterDefinitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AM_ParameterPermissions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    UserRole = table.Column<int>(type: "INTEGER", nullable: false, comment: "用户角色"),
                    ParameterDefinitionId = table.Column<int>(type: "INTEGER", nullable: false),
                    PermissionLevel = table.Column<int>(type: "INTEGER", nullable: false, comment: "权限级别"),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AM_ParameterPermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AM_ParameterPermissions_AM_ParameterDefinitions_ParameterDefinitionId",
                        column: x => x.ParameterDefinitionId,
                        principalTable: "AM_ParameterDefinitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AM_MonitoringData",
                columns: table => new
                {
                    Id = table.Column<long>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    DeviceId = table.Column<int>(type: "INTEGER", nullable: false),
                    DeviceParameterId = table.Column<int>(type: "INTEGER", nullable: false),
                    ParameterIndex = table.Column<byte>(type: "INTEGER", nullable: false, comment: "参数索引"),
                    RawValue = table.Column<ushort>(type: "INTEGER", nullable: false, comment: "原始值"),
                    ParsedValue = table.Column<string>(type: "TEXT", nullable: true, comment: "解析后的值JSON"),
                    NumericValue = table.Column<decimal>(type: "TEXT", precision: 18, scale: 6, nullable: true, comment: "数值型值"),
                    StringValue = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true, comment: "字符串值"),
                    BooleanValue = table.Column<bool>(type: "INTEGER", nullable: true),
                    Quality = table.Column<int>(type: "INTEGER", nullable: false, comment: "数据质量"),
                    Timestamp = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AM_MonitoringData", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AM_MonitoringData_AM_DeviceParameters_DeviceParameterId",
                        column: x => x.DeviceParameterId,
                        principalTable: "AM_DeviceParameters",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AM_MonitoringData_AM_Devices_DeviceId",
                        column: x => x.DeviceId,
                        principalTable: "AM_Devices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CommunicationLogs_CommandCode",
                table: "AM_CommunicationLogs",
                column: "CommandCode");

            migrationBuilder.CreateIndex(
                name: "IX_CommunicationLogs_Device_Time",
                table: "AM_CommunicationLogs",
                columns: new[] { "DeviceId", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_CommunicationLogs_IsSuccessful",
                table: "AM_CommunicationLogs",
                column: "IsSuccessful");

            migrationBuilder.CreateIndex(
                name: "IX_CommunicationLogs_Timestamp",
                table: "AM_CommunicationLogs",
                column: "Timestamp");

            migrationBuilder.CreateIndex(
                name: "IX_AM_DeviceParameters_ParameterDefinitionId",
                table: "AM_DeviceParameters",
                column: "ParameterDefinitionId");

            migrationBuilder.CreateIndex(
                name: "IX_DeviceParameters_Device_Parameter",
                table: "AM_DeviceParameters",
                columns: new[] { "DeviceId", "ParameterDefinitionId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DeviceParameters_IsEnabled",
                table: "AM_DeviceParameters",
                column: "IsEnabled");

            migrationBuilder.CreateIndex(
                name: "IX_Devices_DeviceAddress",
                table: "AM_Devices",
                column: "DeviceAddress",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Devices_DeviceType",
                table: "AM_Devices",
                column: "DeviceType");

            migrationBuilder.CreateIndex(
                name: "IX_Devices_IsOnline",
                table: "AM_Devices",
                column: "IsOnline");

            migrationBuilder.CreateIndex(
                name: "IX_Devices_SerialNumber",
                table: "AM_Devices",
                column: "SerialNumber");

            migrationBuilder.CreateIndex(
                name: "IX_AM_MonitoringData_DeviceParameterId",
                table: "AM_MonitoringData",
                column: "DeviceParameterId");

            migrationBuilder.CreateIndex(
                name: "IX_MonitoringData_CreatedAt",
                table: "AM_MonitoringData",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_MonitoringData_Device_Parameter_Time",
                table: "AM_MonitoringData",
                columns: new[] { "DeviceId", "ParameterIndex", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_MonitoringData_Quality",
                table: "AM_MonitoringData",
                column: "Quality");

            migrationBuilder.CreateIndex(
                name: "IX_MonitoringData_Timestamp",
                table: "AM_MonitoringData",
                column: "Timestamp");

            migrationBuilder.CreateIndex(
                name: "IX_AM_ParameterDefinitions_ParameterTemplateId",
                table: "AM_ParameterDefinitions",
                column: "ParameterTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_ParameterDefinitions_Category",
                table: "AM_ParameterDefinitions",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_ParameterDefinitions_Command_Index",
                table: "AM_ParameterDefinitions",
                columns: new[] { "CommandCode", "ParameterIndex" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ParameterDefinitions_IsActive",
                table: "AM_ParameterDefinitions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_AM_ParameterPermissions_ParameterDefinitionId",
                table: "AM_ParameterPermissions",
                column: "ParameterDefinitionId");

            migrationBuilder.CreateIndex(
                name: "IX_ParameterPermissions_IsActive",
                table: "AM_ParameterPermissions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_ParameterPermissions_Role_Parameter",
                table: "AM_ParameterPermissions",
                columns: new[] { "UserRole", "ParameterDefinitionId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ParameterTemplates_DeviceType_ModelPattern",
                table: "AM_ParameterTemplates",
                columns: new[] { "ApplicableDeviceType", "ModelPattern" });

            migrationBuilder.CreateIndex(
                name: "IX_ParameterTemplates_Name",
                table: "AM_ParameterTemplates",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_AM_UserDevicePermissions_DeviceId",
                table: "AM_UserDevicePermissions",
                column: "DeviceId");

            migrationBuilder.CreateIndex(
                name: "IX_UserDevicePermissions_ExpiresAt",
                table: "AM_UserDevicePermissions",
                column: "ExpiresAt");

            migrationBuilder.CreateIndex(
                name: "IX_UserDevicePermissions_IsActive",
                table: "AM_UserDevicePermissions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_UserDevicePermissions_User_Device",
                table: "AM_UserDevicePermissions",
                columns: new[] { "UserId", "DeviceId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Users_Email",
                table: "AM_Users",
                column: "Email",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Users_IsActive",
                table: "AM_Users",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Role",
                table: "AM_Users",
                column: "Role");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Username",
                table: "AM_Users",
                column: "Username",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AM_CommunicationLogs");

            migrationBuilder.DropTable(
                name: "AM_MonitoringData");

            migrationBuilder.DropTable(
                name: "AM_ParameterPermissions");

            migrationBuilder.DropTable(
                name: "AM_UserDevicePermissions");

            migrationBuilder.DropTable(
                name: "AM_DeviceParameters");

            migrationBuilder.DropTable(
                name: "AM_Users");

            migrationBuilder.DropTable(
                name: "AM_Devices");

            migrationBuilder.DropTable(
                name: "AM_ParameterDefinitions");

            migrationBuilder.DropTable(
                name: "AM_ParameterTemplates");
        }
    }
}
