# AirMonitor - 商用空调监控系统

## 项目概述
**项目类型：** 分布式工业物联网监控系统
**业务领域：** 商用空调设备远程监控与管理
**目标用户：** 空调设备制造商、运维服务商、企业用户
**核心价值：** 通过RS485串口通信实现空调设备的远程监控、控制和数据分析

## .NET 技术栈
- **.NET 版本：** .NET 8.0
- **Web 框架：** ASP.NET Core 8.0 Web API
- **前端框架：** Blazor Server 8.0
- **Windows 服务：** .NET 8.0 Windows Service
- **ORM 框架：** Entity Framework Core 8.0
- **数据库：** SQL Server 2022
- **实时通信：** SignalR
- **串口通信：** System.IO.Ports
- **身份验证：** ASP.NET Core Identity + JWT Bearer
- **API 文档：** Swagger/OpenAPI
- **测试框架：** xUnit
- **日志框架：** Serilog
- **缓存：** Redis + 内存缓存
- **消息队列：** 内置后台服务

## 解决方案架构
```
AirMonitor/
├── src/
│   ├── AirMonitor.Api/                    # Web API 服务层
│   ├── AirMonitor.Web/                    # Blazor Server 前端
│   ├── AirMonitor.WindowsService/         # Windows 本地服务
│   ├── AirMonitor.Core/                   # 核心业务逻辑层
│   ├── AirMonitor.Infrastructure/         # 基础设施层
│   ├── AirMonitor.Shared/                 # 共享组件层
│   └── AirMonitor.Protocols/              # 通信协议层
├── tests/
│   ├── AirMonitor.UnitTests/              # 单元测试
│   ├── AirMonitor.IntegrationTests/       # 集成测试
│   └── AirMonitor.PerformanceTests/       # 性能测试
├── docs/                                  # 项目文档
│   ├── api/                               # API 文档
│   ├── architecture/                      # 架构文档
│   ├── protocols/                         # 通信协议文档
│   └── deployment/                        # 部署文档
└── scripts/                               # 构建和部署脚本
```

## 核心业务模块规划

### 1. 串口通信模块 (SerialCommunication)
- **RS485 总线通信管理**
- **多设备并发连接支持**
- **通信协议解析引擎**
- **数据帧校验和错误处理**

### 2. 数据采集模块 (DataCollection)
- **实时数据监听服务**
- **多协议数据解析器**
- **数据缓存和批量处理**
- **高频数据写入优化**

### 3. 设备控制模块 (DeviceControl)
- **远程参数设置**
- **运行状态控制**
- **指令队列管理**
- **控制结果反馈**

### 4. 配置管理模块 (ConfigurationManagement)
- **EEPROM 参数读写**
- **设备配置模板**
- **参数验证和备份**
- **批量配置部署**

### 5. 监控面板模块 (MonitoringDashboard)
- **实时数据展示**
- **设备状态监控**
- **告警信息管理**
- **多设备视图切换**

### 6. 数据可视化模块 (DataVisualization)
- **实时曲线图表**
- **历史数据分析**
- **自定义图表配置**
- **数据导出功能**

### 7. 数据回放模块 (DataPlayback)
- **时间轴数据回放**
- **回放速度控制**
- **关键事件标记**
- **回放数据分析**

### 8. 用户管理模块 (UserManagement)
- **用户认证和授权**
- **角色权限管理**
- **多租户支持**
- **操作日志审计**

## 技术架构设计

### 分层架构模式
```
┌─────────────────────────────────────────────────┐
│                 表示层 (Presentation)            │
│  Blazor Server Web UI + RESTful API            │
├─────────────────────────────────────────────────┤
│                 应用层 (Application)             │
│  业务服务 + SignalR Hubs + 后台服务            │
├─────────────────────────────────────────────────┤
│                 领域层 (Domain)                  │
│  业务实体 + 领域服务 + 业务规则                │
├─────────────────────────────────────────────────┤
│               基础设施层 (Infrastructure)        │
│  数据访问 + 串口通信 + 外部服务集成            │
└─────────────────────────────────────────────────┘
```

### 数据流架构
```
空调设备 ↔ RS485总线 ↔ 串口 ↔ Windows Service ↔ Web API ↔ SignalR ↔ Blazor UI
                                      ↓
                                 SQL Server 数据库
                                      ↓
                                 Redis 缓存层
```

## 开发流程与状态跟踪
| 阶段 | 模块/功能 | 开发文档 | 实现状态 | 测试状态 | 负责人 | 计划完成 | 实际完成 | 备注 |
|------|-----------|----------|----------|----------|--------|----------|----------|------|
| 1    | 项目初始化 | ✅ | ✅ | ✅ | AI | 2025-01-15 | 2025-01-15 | 解决方案结构已创建 |
| 2    | 基础架构搭建 | ✅ | ✅ | ✅ | AI | 2025-01-16 | 2025-01-15 | 配置管理、DI、中间件、健康检查 |
| 3    | 数据模型设计 | ✅ | ✅ | ✅ | AI | 2025-01-17 | 2025-01-17 | EF Core 实体设计完成，API测试通过 |
| 4    | 串口通信模块 | ✅ | ❌ | ❌ | AI | 2025-01-18 | | RS485 通信核心 |
| 5    | 数据采集服务 | ❌ | ❌ | ❌ | AI | 2025-01-19 | | Windows Service |
| 6    | Web API 开发 | ❌ | ❌ | ❌ | AI | 2025-01-20 | | RESTful 接口 |
| 7    | SignalR 实时通信 | ❌ | ❌ | ❌ | AI | 2025-01-21 | | 实时数据推送 |
| 8    | Blazor 前端界面 | ❌ | ❌ | ❌ | AI | 2025-01-22 | | 用户界面开发 |
| 9    | 设备控制功能 | ❌ | ❌ | ❌ | AI | 2025-01-23 | | 远程控制实现 |
| 10   | 数据可视化 | ❌ | ❌ | ❌ | AI | 2025-01-24 | | 图表和回放 |
| 11   | 用户管理系统 | ❌ | ❌ | ❌ | AI | 2025-01-25 | | 认证授权 |
| 12   | 系统集成测试 | ❌ | ❌ | ❌ | AI | 2025-01-26 | | 端到端测试 |
| 13   | 性能优化 | ❌ | ❌ | ❌ | AI | 2025-01-27 | | 性能调优 |
| 14   | 部署准备 | ❌ | ❌ | ❌ | AI | 2025-01-28 | | 生产环境配置 |

## 质量保证标准
- **代码覆盖率：** ≥ 85%
- **单元测试：** 每个业务方法必须有对应测试
- **集成测试：** 每个 API 端点必须有集成测试
- **性能要求：** 
  - API 响应时间 < 200ms
  - 串口数据处理延迟 < 50ms
  - 支持 1000+ 并发连接
  - 数据库写入性能 > 10000 条/秒
- **安全标准：** 遵循 OWASP 安全指南
- **可靠性：** 系统可用性 ≥ 99.9%

## 核心技术挑战与解决方案
1. **高频数据处理：** 使用批量写入和异步处理
2. **实时性要求：** SignalR + 内存缓存优化
3. **串口并发：** 线程安全的串口管理器
4. **协议兼容：** 可扩展的协议解析框架
5. **数据一致性：** 事务管理和补偿机制

## 项目初始化完成状态

### ✅ 已完成的工作
1. **解决方案结构创建**
   - ✅ AirMonitor.sln 解决方案文件
   - ✅ 8个项目已创建并添加到解决方案
   - ✅ 标准的分层架构目录结构

2. **核心项目结构**
   ```
   ✅ src/AirMonitor.Api/              # Web API 服务 (.NET 8.0)
   ✅ src/AirMonitor.Web/              # Blazor Server 前端 (.NET 8.0)
   ✅ src/AirMonitor.WindowsService/   # Windows 服务 (.NET 8.0)
   ✅ src/AirMonitor.Core/             # 核心业务逻辑层
   ✅ src/AirMonitor.Infrastructure/   # 基础设施层
   ✅ src/AirMonitor.Shared/           # 共享组件层
   ✅ src/AirMonitor.Protocols/        # 通信协议层
   ```

3. **测试项目结构**
   ```
   ✅ tests/AirMonitor.UnitTests/        # 单元测试 (xUnit)
   ✅ tests/AirMonitor.IntegrationTests/ # 集成测试 (xUnit)
   ```

4. **核心 NuGet 包已安装**
   - ✅ Microsoft.EntityFrameworkCore.SqlServer (9.0.5)
   - ✅ Microsoft.EntityFrameworkCore.Tools (9.0.5)
   - ✅ Microsoft.AspNetCore.SignalR (1.2.0)
   - ✅ Serilog.AspNetCore (9.0.0)
   - ✅ System.IO.Ports (9.0.5) - 串口通信
   - ✅ Microsoft.Extensions.Hosting.WindowsServices (9.0.5)

5. **构建验证**
   - ✅ 整个解决方案编译成功
   - ✅ 所有项目依赖关系正确
   - ✅ 构建脚本已创建 (scripts/build.ps1)

6. **基础架构搭建完成**
   - ✅ **配置管理系统** - 强类型配置类和验证器
   - ✅ **依赖注入容器** - 服务注册和生命周期管理
   - ✅ **中间件管道** - 异常处理和请求日志记录
   - ✅ **健康检查系统** - 串口和应用程序健康检查
   - ✅ **CORS 配置** - 跨域请求支持
   - ✅ **Serilog 日志** - 结构化日志记录
   - ✅ **API 文档** - Swagger/OpenAPI 集成
   - ✅ **单元测试** - 基础架构组件测试通过

### 🚀 快速开始指南
1. **还原依赖包**
   ```bash
   dotnet restore
   ```

2. **构建解决方案**
   ```bash
   dotnet build
   ```

3. **运行测试**
   ```bash
   dotnet test
   ```

4. **使用构建脚本**
   ```powershell
   .\scripts\build.ps1 -Configuration Release -RunTests
   ```

## 下一步开发计划

### 🎯 立即可开始的模块
1. ~~**数据模型设计**~~ - ✅ **已完成并测试通过**
2. **串口通信模块** - 实现 RS485 通信核心功能
3. ~~**基础架构搭建**~~ - ✅ **已完成**

### 📝 推荐开发指令
```
/开发 串口通信模块    # 实现 RS485 通信功能 (文档已完成)
/文档 数据采集服务    # 设计数据采集服务
/文档 协议解析模块    # 设计协议解析器
```

### 🎉 数据模型设计完成情况
- ✅ **Entity Framework Core 实体模型**：9个核心实体，支持RS485设备管理
- ✅ **数据库配置**：SQLite开发环境，SQL Server生产环境
- ✅ **仓储模式实现**：完整的数据访问层
- ✅ **种子数据**：参数模板和用户数据自动初始化
- ✅ **API接口测试**：设备管理API测试通过
- ✅ **数据库迁移**：自动创建表结构和索引

## 串口通信模块开发文档完成情况
- ✅ **技术架构设计**：分层架构，包含服务层、协议层、设备管理层、串口驱动层
- ✅ **接口设计**：ISerialCommunicationService、IProtocolManager、IDeviceManager核心接口
- ✅ **协议实现方案**：支持两种RS485数据帧格式（参数索引格式、自定义数据格式）
- ✅ **设备管理策略**：动态设备发现、状态监控、轮询调度机制
- ✅ **性能优化方案**：异步处理、数据缓存、批量处理、错误重试策略
- ✅ **测试计划**：单元测试、集成测试、性能测试覆盖方案
- ✅ **安全考虑**：CRC16校验、访问控制、异常处理、资源保护

---
**项目创建时间：** 2025-01-15
**最后更新时间：** 2025-01-17 (串口通信模块开发文档完成)
**项目负责人：** AI Assistant
**技术栈版本：** .NET 8.0
