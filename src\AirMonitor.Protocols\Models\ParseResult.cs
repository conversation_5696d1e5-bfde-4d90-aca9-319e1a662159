using AirMonitor.Core.Enums;

namespace AirMonitor.Protocols.Models;

/// <summary>
/// 协议解析结果
/// </summary>
public class ParseResult
{
    /// <summary>
    /// 解析是否成功
    /// </summary>
    public bool IsValid { get; set; }
    
    /// <summary>
    /// 协议格式
    /// </summary>
    public ProtocolFormat Format { get; set; }
    
    /// <summary>
    /// 源地址
    /// </summary>
    public byte SourceAddress { get; set; }
    
    /// <summary>
    /// 目标地址
    /// </summary>
    public byte TargetAddress { get; set; }
    
    /// <summary>
    /// 命令码
    /// </summary>
    public byte CommandCode { get; set; }
    
    /// <summary>
    /// 数据长度
    /// </summary>
    public byte DataLength { get; set; }
    
    /// <summary>
    /// 原始数据
    /// </summary>
    public byte[]? RawData { get; set; }
    
    /// <summary>
    /// 解析后的参数数据（用于参数索引格式）
    /// </summary>
    public ParameterData[]? Parameters { get; set; }
    
    /// <summary>
    /// 自定义数据（用于自定义数据格式）
    /// </summary>
    public Dictionary<string, object>? CustomData { get; set; }
    
    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 解析时间戳
    /// </summary>
    public DateTimeOffset Timestamp { get; set; } = DateTimeOffset.UtcNow;
    
    /// <summary>
    /// CRC校验是否通过
    /// </summary>
    public bool CrcValid { get; set; }
    
    /// <summary>
    /// 计算得到的CRC值
    /// </summary>
    public ushort CalculatedCrc { get; set; }
    
    /// <summary>
    /// 帧中的CRC值
    /// </summary>
    public ushort FrameCrc { get; set; }
}

/// <summary>
/// 参数数据
/// </summary>
public class ParameterData
{
    /// <summary>
    /// 参数索引
    /// </summary>
    public byte Index { get; set; }
    
    /// <summary>
    /// 原始值（2字节）
    /// </summary>
    public ushort RawValue { get; set; }
    
    /// <summary>
    /// 解析后的值
    /// </summary>
    public object? ParsedValue { get; set; }
    
    /// <summary>
    /// 参数值格式
    /// </summary>
    public ParameterValueFormat Format { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }
    
    /// <summary>
    /// 数据质量
    /// </summary>
    public DataQuality Quality { get; set; } = DataQuality.Good;
    
    /// <summary>
    /// 参数名称
    /// </summary>
    public string? Name { get; set; }
    
    /// <summary>
    /// 参数描述
    /// </summary>
    public string? Description { get; set; }
}

/// <summary>
/// 设备信息
/// </summary>
public class DeviceInfo
{
    /// <summary>
    /// 设备地址
    /// </summary>
    public byte Address { get; set; }
    
    /// <summary>
    /// 设备类型
    /// </summary>
    public DeviceType DeviceType { get; set; }
    
    /// <summary>
    /// 序列号
    /// </summary>
    public string? SerialNumber { get; set; }
    
    /// <summary>
    /// 型号
    /// </summary>
    public string? ModelNumber { get; set; }
    
    /// <summary>
    /// 设备状态
    /// </summary>
    public DeviceStatus Status { get; set; }
    
    /// <summary>
    /// 最后通信时间
    /// </summary>
    public DateTimeOffset LastCommunication { get; set; }
    
    /// <summary>
    /// 轮询间隔
    /// </summary>
    public TimeSpan PollingInterval { get; set; } = TimeSpan.FromSeconds(5);
    
    /// <summary>
    /// 设备属性
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();
    
    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline => Status == DeviceStatus.Normal && 
                           (DateTimeOffset.UtcNow - LastCommunication) < TimeSpan.FromMinutes(2);
    
    /// <summary>
    /// 支持的命令列表
    /// </summary>
    public List<byte> SupportedCommands { get; set; } = new();
    
    /// <summary>
    /// 固件版本
    /// </summary>
    public string? FirmwareVersion { get; set; }
    
    /// <summary>
    /// 硬件版本
    /// </summary>
    public string? HardwareVersion { get; set; }
}

/// <summary>
/// 通信事件参数基类
/// </summary>
public abstract class CommunicationEventArgs : EventArgs
{
    /// <summary>
    /// 设备地址
    /// </summary>
    public byte DeviceAddress { get; set; }
    
    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTimeOffset Timestamp { get; set; } = DateTimeOffset.UtcNow;
}

/// <summary>
/// 设备数据接收事件参数
/// </summary>
public class DeviceDataReceivedEventArgs : CommunicationEventArgs
{
    /// <summary>
    /// 解析结果
    /// </summary>
    public ParseResult ParseResult { get; set; } = new();
}

/// <summary>
/// 设备状态变化事件参数
/// </summary>
public class DeviceStatusChangedEventArgs : CommunicationEventArgs
{
    /// <summary>
    /// 旧状态
    /// </summary>
    public DeviceStatus OldStatus { get; set; }
    
    /// <summary>
    /// 新状态
    /// </summary>
    public DeviceStatus NewStatus { get; set; }
}

/// <summary>
/// 通信错误事件参数
/// </summary>
public class CommunicationErrorEventArgs : CommunicationEventArgs
{
    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// 异常对象
    /// </summary>
    public Exception? Exception { get; set; }
    
    /// <summary>
    /// 错误类型
    /// </summary>
    public CommunicationErrorType ErrorType { get; set; }
}

/// <summary>
/// 通信错误类型
/// </summary>
public enum CommunicationErrorType
{
    /// <summary>
    /// 连接错误
    /// </summary>
    ConnectionError,
    
    /// <summary>
    /// 超时错误
    /// </summary>
    TimeoutError,
    
    /// <summary>
    /// 协议错误
    /// </summary>
    ProtocolError,
    
    /// <summary>
    /// CRC校验错误
    /// </summary>
    CrcError,
    
    /// <summary>
    /// 设备无响应
    /// </summary>
    NoResponse,
    
    /// <summary>
    /// 其他错误
    /// </summary>
    OtherError
}
