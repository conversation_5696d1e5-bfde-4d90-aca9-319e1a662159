using System.Collections.Concurrent;
using System.IO.Ports;
using AirMonitor.Infrastructure.Configuration;
using AirMonitor.Protocols.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace AirMonitor.Infrastructure.Communication;

/// <summary>
/// 串口管理器实现
/// </summary>
public class SerialPortManager : ISerialPortManager, IDisposable
{
    private readonly ILogger<SerialPortManager> _logger;
    private readonly SerialPortSettings _settings;
    private readonly SemaphoreSlim _operationSemaphore;
    private readonly ConcurrentQueue<byte[]> _receiveQueue;
    private readonly CancellationTokenSource _cancellationTokenSource;
    
    private SerialPort? _serialPort;
    private Task? _receiveTask;
    private bool _disposed;

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected => _serialPort?.IsOpen == true;

    /// <summary>
    /// 当前端口名称
    /// </summary>
    public string? CurrentPortName => _serialPort?.PortName;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    public event EventHandler<byte[]>? DataReceived;

    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    public event EventHandler<bool>? ConnectionStateChanged;

    /// <summary>
    /// 错误事件
    /// </summary>
    public event EventHandler<Exception>? ErrorOccurred;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="settings">串口设置</param>
    public SerialPortManager(ILogger<SerialPortManager> logger, IOptions<SerialPortSettings> settings)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _settings = settings?.Value ?? throw new ArgumentNullException(nameof(settings));
        
        _operationSemaphore = new SemaphoreSlim(1, 1);
        _receiveQueue = new ConcurrentQueue<byte[]>();
        _cancellationTokenSource = new CancellationTokenSource();
    }

    /// <summary>
    /// 连接串口
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接是否成功</returns>
    public async Task<bool> ConnectAsync(string portName, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(portName))
            throw new ArgumentException("端口名称不能为空", nameof(portName));

        await _operationSemaphore.WaitAsync(cancellationToken);
        try
        {
            // 如果已连接到相同端口，直接返回成功
            if (IsConnected && _serialPort?.PortName == portName)
            {
                _logger.LogInformation("串口已连接: {PortName}", portName);
                return true;
            }

            // 断开现有连接
            if (IsConnected)
            {
                await DisconnectInternalAsync();
            }

            _logger.LogInformation("正在连接串口: {PortName}", portName);

            // 创建新的串口实例
            _serialPort = new SerialPort(portName)
            {
                BaudRate = _settings.BaudRate,
                DataBits = _settings.DataBits,
                StopBits = _settings.StopBits,
                Parity = _settings.Parity,
                ReadTimeout = _settings.ReadTimeout,
                WriteTimeout = _settings.WriteTimeout,
                ReceivedBytesThreshold = 1,
                DtrEnable = _settings.DtrEnable,
                RtsEnable = _settings.RtsEnable
            };

            // 设置缓冲区大小
            _serialPort.ReadBufferSize = _settings.ReceiveBufferSize;
            _serialPort.WriteBufferSize = _settings.SendBufferSize;

            // 注册事件处理器
            _serialPort.DataReceived += OnDataReceived;
            _serialPort.ErrorReceived += OnErrorReceived;

            // 尝试打开串口
            await Task.Run(() => _serialPort.Open(), cancellationToken);

            if (_serialPort.IsOpen)
            {
                // 清空缓冲区
                _serialPort.DiscardInBuffer();
                _serialPort.DiscardOutBuffer();

                // 启动数据接收任务
                _receiveTask = Task.Run(() => ReceiveDataLoop(_cancellationTokenSource.Token), 
                                      _cancellationTokenSource.Token);

                _logger.LogInformation("串口连接成功: {PortName}, 波特率: {BaudRate}", 
                                     portName, _settings.BaudRate);

                // 触发连接状态变化事件
                ConnectionStateChanged?.Invoke(this, true);
                return true;
            }
            else
            {
                _logger.LogError("串口打开失败: {PortName}", portName);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接串口失败: {PortName}", portName);
            ErrorOccurred?.Invoke(this, ex);
            return false;
        }
        finally
        {
            _operationSemaphore.Release();
        }
    }

    /// <summary>
    /// 断开连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>断开是否成功</returns>
    public async Task<bool> DisconnectAsync(CancellationToken cancellationToken = default)
    {
        await _operationSemaphore.WaitAsync(cancellationToken);
        try
        {
            return await DisconnectInternalAsync();
        }
        finally
        {
            _operationSemaphore.Release();
        }
    }

    /// <summary>
    /// 发送数据
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送是否成功</returns>
    public async Task<bool> SendAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        if (data == null || data.Length == 0)
            throw new ArgumentException("发送数据不能为空", nameof(data));

        if (!IsConnected)
        {
            _logger.LogWarning("串口未连接，无法发送数据");
            return false;
        }

        try
        {
            await Task.Run(() => _serialPort!.Write(data, 0, data.Length), cancellationToken);
            
            _logger.LogDebug("发送数据成功: {DataLength} 字节, 数据: {Data}", 
                           data.Length, Convert.ToHexString(data));
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送数据失败: {DataLength} 字节", data.Length);
            ErrorOccurred?.Invoke(this, ex);
            return false;
        }
    }

    /// <summary>
    /// 接收数据
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收到的数据</returns>
    public async Task<byte[]?> ReceiveAsync(TimeSpan timeout, CancellationToken cancellationToken = default)
    {
        if (!IsConnected)
        {
            _logger.LogWarning("串口未连接，无法接收数据");
            return null;
        }

        var timeoutCts = new CancellationTokenSource(timeout);
        var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
            cancellationToken, timeoutCts.Token);

        try
        {
            // 等待接收队列中有数据
            while (!combinedCts.Token.IsCancellationRequested)
            {
                if (_receiveQueue.TryDequeue(out var data))
                {
                    _logger.LogDebug("接收数据成功: {DataLength} 字节, 数据: {Data}", 
                                   data.Length, Convert.ToHexString(data));
                    return data;
                }

                await Task.Delay(10, combinedCts.Token);
            }

            _logger.LogDebug("接收数据超时: {Timeout}ms", timeout.TotalMilliseconds);
            return null;
        }
        catch (OperationCanceledException)
        {
            _logger.LogDebug("接收数据被取消");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "接收数据失败");
            ErrorOccurred?.Invoke(this, ex);
            return null;
        }
        finally
        {
            timeoutCts.Dispose();
            combinedCts.Dispose();
        }
    }

    /// <summary>
    /// 清空接收缓冲区
    /// </summary>
    public void ClearReceiveBuffer()
    {
        try
        {
            _serialPort?.DiscardInBuffer();
            
            // 清空接收队列
            while (_receiveQueue.TryDequeue(out _)) { }
            
            _logger.LogDebug("接收缓冲区已清空");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清空接收缓冲区失败");
        }
    }

    /// <summary>
    /// 清空发送缓冲区
    /// </summary>
    public void ClearSendBuffer()
    {
        try
        {
            _serialPort?.DiscardOutBuffer();
            _logger.LogDebug("发送缓冲区已清空");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清空发送缓冲区失败");
        }
    }

    /// <summary>
    /// 获取可用串口列表
    /// </summary>
    /// <returns>串口名称列表</returns>
    public string[] GetAvailablePorts()
    {
        try
        {
            var ports = SerialPort.GetPortNames();
            _logger.LogDebug("可用串口: {Ports}", string.Join(", ", ports));
            return ports;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可用串口列表失败");
            return Array.Empty<string>();
        }
    }

    /// <summary>
    /// 内部断开连接方法
    /// </summary>
    /// <returns>断开是否成功</returns>
    private async Task<bool> DisconnectInternalAsync()
    {
        try
        {
            var wasConnected = IsConnected;

            // 取消接收任务
            _cancellationTokenSource.Cancel();
            if (_receiveTask != null)
            {
                await _receiveTask;
                _receiveTask = null;
            }

            // 关闭串口
            if (_serialPort != null)
            {
                if (_serialPort.IsOpen)
                {
                    _serialPort.Close();
                }

                _serialPort.DataReceived -= OnDataReceived;
                _serialPort.ErrorReceived -= OnErrorReceived;
                _serialPort.Dispose();
                _serialPort = null;
            }

            // 清空接收队列
            while (_receiveQueue.TryDequeue(out _)) { }

            if (wasConnected)
            {
                _logger.LogInformation("串口连接已断开");
                ConnectionStateChanged?.Invoke(this, false);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "断开串口连接失败");
            ErrorOccurred?.Invoke(this, ex);
            return false;
        }
    }

    /// <summary>
    /// 数据接收事件处理器
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnDataReceived(object sender, SerialDataReceivedEventArgs e)
    {
        try
        {
            if (_serialPort?.IsOpen == true && _serialPort.BytesToRead > 0)
            {
                var buffer = new byte[_serialPort.BytesToRead];
                var bytesRead = _serialPort.Read(buffer, 0, buffer.Length);

                if (bytesRead > 0)
                {
                    var data = new byte[bytesRead];
                    Array.Copy(buffer, data, bytesRead);

                    _receiveQueue.Enqueue(data);
                    DataReceived?.Invoke(this, data);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理接收数据事件失败");
            ErrorOccurred?.Invoke(this, ex);
        }
    }

    /// <summary>
    /// 错误接收事件处理器
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnErrorReceived(object sender, SerialErrorReceivedEventArgs e)
    {
        var errorMessage = $"串口错误: {e.EventType}";
        _logger.LogError(errorMessage);
        ErrorOccurred?.Invoke(this, new InvalidOperationException(errorMessage));
    }

    /// <summary>
    /// 数据接收循环
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task ReceiveDataLoop(CancellationToken cancellationToken)
    {
        _logger.LogDebug("数据接收循环已启动");

        try
        {
            while (!cancellationToken.IsCancellationRequested && IsConnected)
            {
                await Task.Delay(10, cancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消，不记录错误
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据接收循环异常");
            ErrorOccurred?.Invoke(this, ex);
        }

        _logger.LogDebug("数据接收循环已停止");
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _cancellationTokenSource.Cancel();
            DisconnectInternalAsync().Wait(5000);

            _operationSemaphore?.Dispose();
            _cancellationTokenSource?.Dispose();

            _disposed = true;
        }
    }
}
