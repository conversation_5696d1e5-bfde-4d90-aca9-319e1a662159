{"openapi": "3.0.1", "info": {"title": "AirMonitor API", "description": "商用空调监控系统 API", "contact": {"name": "AirMonitor Team", "url": "https://www.airmonitor.com", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "v1"}, "paths": {"/api/Devices": {"get": {"tags": ["Devices"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Device"}}}}}}}, "post": {"tags": ["Devices"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Device"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Device"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Device"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Device"}}}}}}}, "/api/Devices/{id}": {"get": {"tags": ["Devices"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Device"}}}}}}}, "/api/Devices/address/{address}": {"get": {"tags": ["Devices"], "parameters": [{"name": "address", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Device"}}}}}}}, "/api/Devices/online": {"get": {"tags": ["Devices"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Device"}}}}}}}}, "/api/Devices/type/{deviceType}": {"get": {"tags": ["Devices"], "parameters": [{"name": "deviceType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/DeviceType"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Device"}}}}}}}}, "/api/Devices/address/{address}/status": {"put": {"tags": ["Devices"], "parameters": [{"name": "address", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}, "application/*+json": {"schema": {"type": "boolean"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MonitoringData/device/{deviceId}/latest": {"get": {"tags": ["MonitoringData"], "parameters": [{"name": "deviceId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 100}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MonitoringData"}}}}}}}}, "/api/MonitoringData/device/{deviceId}/range": {"get": {"tags": ["MonitoringData"], "parameters": [{"name": "deviceId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "start", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "end", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MonitoringData"}}}}}}}}, "/api/MonitoringData/device/{deviceId}/parameter/{parameterIndex}/latest": {"get": {"tags": ["MonitoringData"], "parameters": [{"name": "deviceId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "parameterIndex", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MonitoringData": {"post": {"tags": ["MonitoringData"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MonitoringData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MonitoringData"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MonitoringData"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MonitoringData"}}}}}}}, "/api/status": {"get": {"tags": ["System"], "operationId": "GetStatus", "responses": {"200": {"description": "OK"}}}}, "/api/System/info": {"get": {"tags": ["System"], "responses": {"200": {"description": "OK"}}}}, "/api/System/config": {"get": {"tags": ["System"], "responses": {"200": {"description": "OK"}}}}, "/api/System/health": {"get": {"tags": ["System"], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"CommunicationLog": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "deviceId": {"type": "integer", "format": "int32"}, "communicationType": {"$ref": "#/components/schemas/CommunicationType"}, "protocolFormat": {"$ref": "#/components/schemas/ProtocolFormat"}, "sourceAddress": {"type": "integer", "format": "int32"}, "targetAddress": {"type": "integer", "format": "int32"}, "commandCode": {"type": "integer", "format": "int32"}, "rawData": {"type": "string", "format": "byte", "nullable": true}, "parsedData": {"type": "string", "nullable": true}, "isSuccessful": {"type": "boolean"}, "errorMessage": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "device": {"$ref": "#/components/schemas/Device"}}, "additionalProperties": false}, "CommunicationType": {"enum": [1, 2], "type": "integer", "format": "int32"}, "DataQuality": {"enum": [1, 2, 3], "type": "integer", "format": "int32"}, "Device": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "deviceAddress": {"type": "integer", "format": "int32"}, "deviceType": {"$ref": "#/components/schemas/DeviceType"}, "serialNumber": {"type": "string", "nullable": true}, "modelNumber": {"type": "string", "nullable": true}, "firmwareVersion": {"type": "string", "nullable": true}, "status": {"$ref": "#/components/schemas/DeviceStatus"}, "lastCommunication": {"type": "string", "format": "date-time"}, "isOnline": {"type": "boolean"}, "location": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deviceParameters": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceParameter"}, "nullable": true}, "communicationLogs": {"type": "array", "items": {"$ref": "#/components/schemas/CommunicationLog"}, "nullable": true}, "monitoringData": {"type": "array", "items": {"$ref": "#/components/schemas/MonitoringData"}, "nullable": true}, "userPermissions": {"type": "array", "items": {"$ref": "#/components/schemas/UserDevicePermission"}, "nullable": true}}, "additionalProperties": false}, "DeviceParameter": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "deviceId": {"type": "integer", "format": "int32"}, "parameterDefinitionId": {"type": "integer", "format": "int32"}, "isEnabled": {"type": "boolean"}, "customName": {"type": "string", "nullable": true}, "customConfiguration": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "device": {"$ref": "#/components/schemas/Device"}, "parameterDefinition": {"$ref": "#/components/schemas/ParameterDefinition"}, "monitoringData": {"type": "array", "items": {"$ref": "#/components/schemas/MonitoringData"}, "nullable": true}}, "additionalProperties": false}, "DeviceStatus": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "DeviceType": {"enum": [1, 2, 3], "type": "integer", "format": "int32"}, "MonitoringData": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "deviceId": {"type": "integer", "format": "int32"}, "deviceParameterId": {"type": "integer", "format": "int32"}, "parameterIndex": {"type": "integer", "format": "int32"}, "rawValue": {"type": "integer", "format": "int32"}, "parsedValue": {"type": "string", "nullable": true}, "numericValue": {"type": "number", "format": "double", "nullable": true}, "stringValue": {"type": "string", "nullable": true}, "booleanValue": {"type": "boolean", "nullable": true}, "quality": {"$ref": "#/components/schemas/DataQuality"}, "timestamp": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "device": {"$ref": "#/components/schemas/Device"}, "deviceParameter": {"$ref": "#/components/schemas/DeviceParameter"}}, "additionalProperties": false}, "ParameterDefinition": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "parameterTemplateId": {"type": "integer", "format": "int32"}, "commandCode": {"type": "integer", "format": "int32"}, "parameterIndex": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "dataType": {"type": "string", "nullable": true}, "valueFormat": {"$ref": "#/components/schemas/ParameterValueFormat"}, "formatConfiguration": {"type": "string", "nullable": true}, "unit": {"type": "string", "nullable": true}, "minValue": {"type": "number", "format": "double", "nullable": true}, "maxValue": {"type": "number", "format": "double", "nullable": true}, "isReadOnly": {"type": "boolean"}, "displayOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "parameterTemplate": {"$ref": "#/components/schemas/ParameterTemplate"}, "deviceParameters": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceParameter"}, "nullable": true}}, "additionalProperties": false}, "ParameterTemplate": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "applicableDeviceType": {"$ref": "#/components/schemas/DeviceType"}, "modelPattern": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "parameterDefinitions": {"type": "array", "items": {"$ref": "#/components/schemas/ParameterDefinition"}, "nullable": true}}, "additionalProperties": false}, "ParameterValueFormat": {"enum": [1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "PermissionLevel": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "ProtocolFormat": {"enum": [1, 2], "type": "integer", "format": "int32"}, "User": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "passwordHash": {"type": "string", "nullable": true}, "role": {"$ref": "#/components/schemas/UserRole"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "devicePermissions": {"type": "array", "items": {"$ref": "#/components/schemas/UserDevicePermission"}, "nullable": true}}, "additionalProperties": false}, "UserDevicePermission": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "deviceId": {"type": "integer", "format": "int32"}, "permissionLevel": {"$ref": "#/components/schemas/PermissionLevel"}, "grantedAt": {"type": "string", "format": "date-time"}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "user": {"$ref": "#/components/schemas/User"}, "device": {"$ref": "#/components/schemas/Device"}}, "additionalProperties": false}, "UserRole": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}}}}