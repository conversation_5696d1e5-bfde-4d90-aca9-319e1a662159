using System.Diagnostics;
using System.Text.Json;
using System.Threading.Channels;
using AirMonitor.Infrastructure.Configuration;
using AirMonitor.Infrastructure.DataCollection.Interfaces;
using AirMonitor.Infrastructure.DataCollection.Models;
using AirMonitor.Protocols.Interfaces;
using AirMonitor.Protocols.Models;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace AirMonitor.Infrastructure.DataCollection.Services;

/// <summary>
/// 数据采集服务
/// </summary>
public class DataCollectionService : IDataCollectionService, IDisposable
{
    private readonly ILogger<DataCollectionService> _logger;
    private readonly DataCollectionSettings _settings;
    private readonly ISerialCommunicationService _communicationService;
    private readonly IDataProcessorFactory _processorFactory;
    private readonly IDataValidator _dataValidator;
    private readonly IMemoryCache _memoryCache;
    
    private readonly Channel<ParseResult> _dataChannel;
    private readonly ChannelWriter<ParseResult> _channelWriter;
    private readonly ChannelReader<ParseResult> _channelReader;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private readonly SemaphoreSlim _processingLock;
    
    private Task? _processingTask;
    private readonly DataCollectionStatistics _statistics;
    private readonly Timer _statisticsTimer;
    private bool _disposed;

    /// <summary>
    /// 服务是否正在运行
    /// </summary>
    public bool IsRunning { get; private set; }

    /// <summary>
    /// 数据处理完成事件
    /// </summary>
    public event EventHandler<DataProcessedEventArgs>? DataProcessed;

    /// <summary>
    /// 数据处理错误事件
    /// </summary>
    public event EventHandler<DataProcessingErrorEventArgs>? ProcessingError;

    /// <summary>
    /// 批量处理完成事件
    /// </summary>
    public event EventHandler<BatchProcessedEventArgs>? BatchProcessed;

    public DataCollectionService(
        ILogger<DataCollectionService> logger,
        IOptions<DataCollectionSettings> settings,
        ISerialCommunicationService communicationService,
        IDataProcessorFactory processorFactory,
        IDataValidator dataValidator,
        IMemoryCache memoryCache)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _settings = settings?.Value ?? throw new ArgumentNullException(nameof(settings));
        _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
        _processorFactory = processorFactory ?? throw new ArgumentNullException(nameof(processorFactory));
        _dataValidator = dataValidator ?? throw new ArgumentNullException(nameof(dataValidator));
        _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));

        // 创建数据处理通道
        var channelOptions = new BoundedChannelOptions(_settings.QueueCapacity)
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = false,
            SingleWriter = false
        };
        _dataChannel = Channel.CreateBounded<ParseResult>(channelOptions);
        _channelWriter = _dataChannel.Writer;
        _channelReader = _dataChannel.Reader;

        _cancellationTokenSource = new CancellationTokenSource();
        _processingLock = new SemaphoreSlim(_settings.MaxConcurrentProcessors, _settings.MaxConcurrentProcessors);

        // 初始化统计信息
        _statistics = new DataCollectionStatistics
        {
            LastResetTime = DateTimeOffset.UtcNow
        };

        // 创建统计重置定时器
        _statisticsTimer = new Timer(ResetStatistics, null, Timeout.Infinite, Timeout.Infinite);
    }

    /// <summary>
    /// 启动服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (IsRunning)
        {
            _logger.LogWarning("数据采集服务已在运行中");
            return;
        }

        _logger.LogInformation("启动数据采集服务...");

        try
        {
            // 订阅串口通信服务事件
            _communicationService.DeviceDataReceived += OnDeviceDataReceived;
            _communicationService.DeviceStatusChanged += OnDeviceStatusChanged;

            // 启动数据处理任务
            _processingTask = Task.Run(() => ProcessDataLoop(_cancellationTokenSource.Token), 
                                     _cancellationTokenSource.Token);

            // 启动统计重置定时器
            _statisticsTimer.Change(TimeSpan.FromMilliseconds(_settings.StatisticsResetInterval), 
                                   TimeSpan.FromMilliseconds(_settings.StatisticsResetInterval));

            IsRunning = true;
            _logger.LogInformation("数据采集服务启动成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动数据采集服务失败");
            throw;
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (!IsRunning)
        {
            _logger.LogInformation("数据采集服务未在运行");
            return;
        }

        _logger.LogInformation("停止数据采集服务...");

        try
        {
            // 取消所有任务
            _cancellationTokenSource.Cancel();

            // 停止统计定时器
            _statisticsTimer.Change(Timeout.Infinite, Timeout.Infinite);

            // 关闭数据通道写入端
            _channelWriter.Complete();

            // 等待处理任务完成
            if (_processingTask != null)
            {
                await _processingTask;
            }

            // 取消事件订阅
            _communicationService.DeviceDataReceived -= OnDeviceDataReceived;
            _communicationService.DeviceStatusChanged -= OnDeviceStatusChanged;

            IsRunning = false;
            _logger.LogInformation("数据采集服务已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止数据采集服务失败");
        }
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public async Task<DataCollectionStatistics> GetStatisticsAsync()
    {
        await Task.CompletedTask;
        
        lock (_statistics)
        {
            // 计算处理速率和错误率
            var timeSpan = DateTimeOffset.UtcNow - _statistics.LastResetTime;
            if (timeSpan.TotalSeconds > 0)
            {
                _statistics.ProcessingRate = _statistics.TotalDataProcessed / timeSpan.TotalSeconds;
                _statistics.ErrorRate = _statistics.TotalDataReceived > 0 ? 
                    (double)_statistics.TotalErrors / _statistics.TotalDataReceived : 0;
            }

            return new DataCollectionStatistics
            {
                TotalDataReceived = _statistics.TotalDataReceived,
                TotalDataProcessed = _statistics.TotalDataProcessed,
                TotalDataStored = _statistics.TotalDataStored,
                TotalErrors = _statistics.TotalErrors,
                ProcessingRate = _statistics.ProcessingRate,
                ErrorRate = _statistics.ErrorRate,
                AverageProcessingTime = _statistics.AverageProcessingTime,
                CacheHits = _statistics.CacheHits,
                CacheMisses = _statistics.CacheMisses,
                LastResetTime = _statistics.LastResetTime,
                ProcessorStatistics = new Dictionary<string, long>(_statistics.ProcessorStatistics)
            };
        }
    }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    public async Task ResetStatisticsAsync()
    {
        await Task.CompletedTask;
        
        lock (_statistics)
        {
            _statistics.TotalDataReceived = 0;
            _statistics.TotalDataProcessed = 0;
            _statistics.TotalDataStored = 0;
            _statistics.TotalErrors = 0;
            _statistics.ProcessingRate = 0;
            _statistics.ErrorRate = 0;
            _statistics.AverageProcessingTime = TimeSpan.Zero;
            _statistics.CacheHits = 0;
            _statistics.CacheMisses = 0;
            _statistics.LastResetTime = DateTimeOffset.UtcNow;
            _statistics.ProcessorStatistics.Clear();
        }
        
        _logger.LogInformation("数据采集服务统计信息已重置");
    }

    /// <summary>
    /// 获取缓存数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <returns>缓存数据</returns>
    public async Task<T?> GetCachedDataAsync<T>(string key) where T : class
    {
        await Task.CompletedTask;
        
        if (string.IsNullOrWhiteSpace(key))
            return null;

        if (_memoryCache.TryGetValue(key, out var value))
        {
            lock (_statistics)
            {
                _statistics.CacheHits++;
            }
            return value as T;
        }

        lock (_statistics)
        {
            _statistics.CacheMisses++;
        }
        return null;
    }

    /// <summary>
    /// 设置缓存数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="data">数据</param>
    /// <param name="expiry">过期时间</param>
    public async Task SetCachedDataAsync<T>(string key, T data, TimeSpan? expiry = null) where T : class
    {
        await Task.CompletedTask;
        
        if (string.IsNullOrWhiteSpace(key) || data == null)
            return;

        var expiryTime = expiry ?? TimeSpan.FromMinutes(_settings.CacheExpiryMinutes);
        var cacheOptions = new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = expiryTime,
            Size = 1
        };

        _memoryCache.Set(key, data, cacheOptions);
    }

    /// <summary>
    /// 使缓存失效
    /// </summary>
    /// <param name="pattern">缓存键模式</param>
    public async Task InvalidateCacheAsync(string pattern)
    {
        await Task.CompletedTask;
        
        if (string.IsNullOrWhiteSpace(pattern))
            return;

        // 简化实现：移除特定键
        _memoryCache.Remove(pattern);
        
        _logger.LogDebug("缓存已失效: {Pattern}", pattern);
    }

    /// <summary>
    /// 设备数据接收事件处理器
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnDeviceDataReceived(object? sender, DeviceDataReceivedEventArgs e)
    {
        try
        {
            // 将接收到的数据写入处理队列
            if (!_channelWriter.TryWrite(e.ParseResult))
            {
                _logger.LogWarning("数据处理队列已满，丢弃数据: 设备地址=0x{Address:X2}", 
                                 e.DeviceAddress);
                lock (_statistics)
                {
                    _statistics.TotalErrors++;
                }
            }
            else
            {
                lock (_statistics)
                {
                    _statistics.TotalDataReceived++;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理设备数据接收事件失败");
            lock (_statistics)
            {
                _statistics.TotalErrors++;
            }
        }
    }

    /// <summary>
    /// 设备状态变化事件处理器
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnDeviceStatusChanged(object? sender, DeviceStatusChangedEventArgs e)
    {
        _logger.LogDebug("设备状态变化: 地址=0x{Address:X2}, {OldStatus} -> {NewStatus}",
                       e.DeviceAddress, e.OldStatus, e.NewStatus);
    }

    /// <summary>
    /// 数据处理主循环
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task ProcessDataLoop(CancellationToken cancellationToken)
    {
        _logger.LogDebug("数据处理循环已启动");

        var batch = new List<ParseResult>();
        var batchTimer = Stopwatch.StartNew();

        try
        {
            await foreach (var parseResult in _channelReader.ReadAllAsync(cancellationToken))
            {
                batch.Add(parseResult);

                // 检查批量处理条件
                if (batch.Count >= _settings.BatchSize ||
                    batchTimer.ElapsedMilliseconds >= _settings.BatchTimeout)
                {
                    await ProcessBatchAsync(batch, cancellationToken);
                    batch.Clear();
                    batchTimer.Restart();
                }
            }

            // 处理剩余数据
            if (batch.Count > 0)
            {
                await ProcessBatchAsync(batch, cancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("数据处理循环被取消");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据处理循环异常");
        }

        _logger.LogDebug("数据处理循环已停止");
    }

    /// <summary>
    /// 处理数据批次
    /// </summary>
    /// <param name="batch">数据批次</param>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task ProcessBatchAsync(List<ParseResult> batch, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        var successCount = 0;
        var errorCount = 0;

        try
        {
            _logger.LogDebug("开始处理数据批次: {BatchSize} 条", batch.Count);

            // 并行处理批次数据
            var processingTasks = batch.Select(async parseResult =>
            {
                try
                {
                    await ProcessSingleDataAsync(parseResult, cancellationToken);
                    Interlocked.Increment(ref successCount);
                    lock (_statistics)
                    {
                        _statistics.TotalDataProcessed++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理单条数据失败: 设备地址=0x{Address:X2}",
                                   parseResult.SourceAddress);
                    Interlocked.Increment(ref errorCount);
                    lock (_statistics)
                    {
                        _statistics.TotalErrors++;
                    }

                    // 触发错误事件
                    ProcessingError?.Invoke(this, new DataProcessingErrorEventArgs
                    {
                        OriginalData = parseResult,
                        ErrorMessage = ex.Message,
                        Exception = ex,
                        Timestamp = DateTimeOffset.UtcNow
                    });
                }
            });

            await Task.WhenAll(processingTasks);

            stopwatch.Stop();
            _logger.LogDebug("批次处理完成: 成功={Success}, 失败={Error}, 耗时={ElapsedMs}ms",
                           successCount, errorCount, stopwatch.ElapsedMilliseconds);

            // 更新统计信息
            UpdateProcessingStatistics(stopwatch.Elapsed, successCount + errorCount);

            // 触发批次处理完成事件
            BatchProcessed?.Invoke(this, new BatchProcessedEventArgs
            {
                BatchSize = batch.Count,
                SuccessCount = successCount,
                ErrorCount = errorCount,
                ProcessingTime = stopwatch.Elapsed,
                Timestamp = DateTimeOffset.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批次处理异常");
            lock (_statistics)
            {
                _statistics.TotalErrors += batch.Count;
            }
        }
    }

    /// <summary>
    /// 处理单条数据
    /// </summary>
    /// <param name="parseResult">解析结果</param>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task ProcessSingleDataAsync(ParseResult parseResult, CancellationToken cancellationToken)
    {
        await _processingLock.WaitAsync(cancellationToken);
        try
        {
            var processingStopwatch = Stopwatch.StartNew();

            // 1. 数据验证
            if (_settings.EnableDataValidation)
            {
                var validationResult = await _dataValidator.ValidateAsync(parseResult);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("数据验证失败: {ErrorMessage}", validationResult.ErrorMessage);

                    if (_settings.StrictValidation)
                    {
                        throw new InvalidOperationException($"数据验证失败: {validationResult.ErrorMessage}");
                    }
                }
            }

            // 2. 获取适用的处理器
            var processors = _processorFactory.GetProcessors(parseResult);
            if (!processors.Any())
            {
                _logger.LogWarning("未找到适用的数据处理器: 格式={Format}, 命令码=0x{CommandCode:X2}",
                                 parseResult.Format, parseResult.CommandCode);
                return;
            }

            // 3. 使用第一个处理器处理数据
            var processor = processors.First();
            var processedData = await processor.ProcessAsync(parseResult, cancellationToken);

            processingStopwatch.Stop();

            // 4. 更新处理器统计
            lock (_statistics)
            {
                if (!_statistics.ProcessorStatistics.ContainsKey(processor.Name))
                {
                    _statistics.ProcessorStatistics[processor.Name] = 0;
                }
                _statistics.ProcessorStatistics[processor.Name]++;
                _statistics.TotalDataStored++;
            }

            // 5. 触发数据处理完成事件
            DataProcessed?.Invoke(this, new DataProcessedEventArgs
            {
                ProcessedData = processedData,
                ProcessingTime = processingStopwatch.Elapsed,
                Timestamp = DateTimeOffset.UtcNow
            });
        }
        finally
        {
            _processingLock.Release();
        }
    }

    /// <summary>
    /// 更新处理统计信息
    /// </summary>
    /// <param name="processingTime">处理时间</param>
    /// <param name="processedCount">处理数量</param>
    private void UpdateProcessingStatistics(TimeSpan processingTime, int processedCount)
    {
        lock (_statistics)
        {
            if (processedCount > 0)
            {
                var avgTime = processingTime.TotalMilliseconds / processedCount;

                // 计算移动平均值
                if (_statistics.AverageProcessingTime == TimeSpan.Zero)
                {
                    _statistics.AverageProcessingTime = TimeSpan.FromMilliseconds(avgTime);
                }
                else
                {
                    var currentAvg = _statistics.AverageProcessingTime.TotalMilliseconds;
                    var newAvg = (currentAvg * 0.9) + (avgTime * 0.1); // 指数移动平均
                    _statistics.AverageProcessingTime = TimeSpan.FromMilliseconds(newAvg);
                }
            }
        }
    }

    /// <summary>
    /// 重置统计信息定时器回调
    /// </summary>
    /// <param name="state">状态</param>
    private void ResetStatistics(object? state)
    {
        try
        {
            _ = ResetStatisticsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "定时重置统计信息失败");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            StopAsync().Wait(10000);

            _processingLock?.Dispose();
            _cancellationTokenSource?.Dispose();
            _statisticsTimer?.Dispose();

            _disposed = true;
        }
    }
}
