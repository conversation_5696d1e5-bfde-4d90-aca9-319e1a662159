using AirMonitor.Core.Enums;
using AirMonitor.Protocols.Constants;
using AirMonitor.Protocols.Interfaces;
using AirMonitor.Protocols.Models;

namespace AirMonitor.Protocols.Parsers;

/// <summary>
/// 参数索引格式解析器（变长格式）
/// 格式：7E + 源地址 + 目标地址 + 命令码 + 长度 + 参数索引 + 参数个数 + 参数值... + CRC16
/// 示例：7E F1 F1 12 25 35 0E 01 4F 00 E3 00 F1 02 90 00 00 00 00 00 00 00 00 00 00 00 00 00 3B 10 40 00 00 00 00 40 EC
/// </summary>
public class ParameterIndexParser : IProtocolParser
{
    /// <summary>
    /// 支持的协议格式
    /// </summary>
    public ProtocolFormat SupportedFormat => ProtocolFormat.ParameterIndex;

    /// <summary>
    /// 解析数据帧
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <returns>解析结果</returns>
    public ParseResult ParseFrame(byte[] rawData)
    {
        var result = new ParseResult
        {
            Format = ProtocolFormat.ParameterIndex,
            RawData = rawData,
            Timestamp = DateTimeOffset.UtcNow
        };

        try
        {
            // 1. 基本长度检查
            if (rawData.Length < ProtocolConstants.MIN_FRAME_LENGTH + 2) // 至少需要参数索引和参数个数
            {
                result.ErrorMessage = "参数索引格式帧长度不足";
                return result;
            }

            // 2. 帧头检查
            if (rawData[0] != ProtocolConstants.FRAME_HEADER)
            {
                result.ErrorMessage = $"无效的帧头: 0x{rawData[0]:X2}";
                return result;
            }

            // 3. 提取基本信息
            result.SourceAddress = rawData[1];
            result.TargetAddress = rawData[2];
            result.CommandCode = rawData[3];
            result.DataLength = rawData[4];

            // 4. 验证数据长度
            var expectedFrameLength = result.DataLength + 2; // 数据长度 + 头码 + CRC
            if (rawData.Length != expectedFrameLength)
            {
                result.ErrorMessage = $"帧长度不匹配: 期望{expectedFrameLength}, 实际{rawData.Length}";
                return result;
            }

            // 5. CRC校验
            var crcValid = ValidateCRC(rawData, out var calculatedCrc, out var frameCrc);
            result.CrcValid = crcValid;
            result.CalculatedCrc = calculatedCrc;
            result.FrameCrc = frameCrc;

            if (!crcValid)
            {
                result.ErrorMessage = $"CRC校验失败: 计算值=0x{calculatedCrc:X4}, 帧值=0x{frameCrc:X4}";
                return result;
            }

            // 6. 解析参数数据
            if (rawData.Length >= 8) // 至少包含参数索引和参数个数
            {
                var parameterIndex = rawData[5];
                var parameterCount = rawData[6];

                // 验证参数个数的合理性
                var expectedDataLength = 2 + parameterCount * 2; // 参数索引(1) + 参数个数(1) + 参数值(2*count)
                var actualDataLength = result.DataLength - 5; // 总长度 - 固定部分长度

                if (actualDataLength < expectedDataLength)
                {
                    result.ErrorMessage = $"参数数据长度不足: 期望{expectedDataLength}, 实际{actualDataLength}";
                    return result;
                }

                // 解析参数值
                var parameters = new List<ParameterData>();
                var dataOffset = 7; // 参数值开始位置

                for (int i = 0; i < parameterCount && dataOffset + 1 < rawData.Length - 2; i++)
                {
                    var currentIndex = (byte)(parameterIndex + i);
                    var rawValue = (ushort)((rawData[dataOffset] << 8) | rawData[dataOffset + 1]);

                    var parameter = new ParameterData
                    {
                        Index = currentIndex,
                        RawValue = rawValue,
                        Format = ParameterValueFormat.SingleValue,
                        Quality = DataQuality.Good
                    };

                    // 根据参数索引进行基本的值解析
                    parameter.ParsedValue = ParseParameterValue(currentIndex, rawValue);

                    parameters.Add(parameter);
                    dataOffset += 2;
                }

                result.Parameters = parameters.ToArray();
            }

            result.IsValid = true;
        }
        catch (Exception ex)
        {
            result.ErrorMessage = $"解析异常: {ex.Message}";
            result.IsValid = false;
        }

        return result;
    }

    /// <summary>
    /// 验证数据帧格式
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <returns>是否为有效格式</returns>
    public bool CanParse(byte[] rawData)
    {
        if (rawData.Length < ProtocolConstants.MIN_FRAME_LENGTH)
            return false;

        // 检查帧头
        if (rawData[0] != ProtocolConstants.FRAME_HEADER)
            return false;

        // 检查命令码是否为参数相关命令
        var commandCode = rawData[3];
        return commandCode == ProtocolConstants.CMD_PARAMETER_READ ||
               commandCode == ProtocolConstants.CMD_PARAMETER_WRITE;
    }

    /// <summary>
    /// 验证CRC校验码
    /// </summary>
    /// <param name="frame">数据帧</param>
    /// <param name="calculatedCrc">计算得到的CRC</param>
    /// <param name="frameCrc">帧中的CRC</param>
    /// <returns>校验是否通过</returns>
    private bool ValidateCRC(byte[] frame, out ushort calculatedCrc, out ushort frameCrc)
    {
        calculatedCrc = 0;
        frameCrc = 0;

        if (frame.Length < ProtocolConstants.MIN_FRAME_LENGTH)
            return false;

        try
        {
            // CRC计算范围：从源地址到数据结束（不包括头码和CRC本身）
            var crcStart = 1; // 从源地址开始
            var crcLength = frame.Length - 3; // 减去头码和2字节CRC

            calculatedCrc = CalculateModbusCRC16(frame, crcStart, crcLength);
            frameCrc = (ushort)((frame[frame.Length - 2] << 8) | frame[frame.Length - 1]);

            return calculatedCrc == frameCrc;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 计算Modbus CRC16校验码
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="start">起始位置</param>
    /// <param name="length">长度</param>
    /// <returns>CRC16值</returns>
    private ushort CalculateModbusCRC16(byte[] data, int start, int length)
    {
        ushort crc = ProtocolConstants.CRC16_INITIAL_VALUE;

        for (int i = start; i < start + length; i++)
        {
            crc ^= data[i];
            for (int j = 0; j < 8; j++)
            {
                if ((crc & 0x0001) != 0)
                {
                    crc >>= 1;
                    crc ^= ProtocolConstants.CRC16_POLYNOMIAL;
                }
                else
                {
                    crc >>= 1;
                }
            }
        }

        return crc;
    }

    /// <summary>
    /// 解析参数值
    /// </summary>
    /// <param name="parameterIndex">参数索引</param>
    /// <param name="rawValue">原始值</param>
    /// <returns>解析后的值</returns>
    private object ParseParameterValue(byte parameterIndex, ushort rawValue)
    {
        // 根据参数索引进行特定的值解析
        // 这里提供一些常见参数的解析示例
        return parameterIndex switch
        {
            0x35 => rawValue / 10.0, // 额定容量，单位：kW，精度0.1
            0x36 => (rawValue - 500) / 10.0, // 温度值，偏移500，精度0.1°C
            0x37 => (rawValue - 500) / 10.0, // 温度值，偏移500，精度0.1°C
            >= 0x40 and <= 0x4F => rawValue / 100.0, // 压力值，精度0.01MPa
            >= 0x50 and <= 0x5F => rawValue, // 状态值，直接使用
            _ => rawValue // 默认返回原始值
        };
    }
}
