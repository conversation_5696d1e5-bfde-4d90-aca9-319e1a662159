using AirMonitor.Core.Enums;
using AirMonitor.Protocols.Constants;
using AirMonitor.Protocols.Interfaces;
using AirMonitor.Protocols.Models;
using AirMonitor.Protocols.Parsers;

namespace AirMonitor.Protocols.Services;

/// <summary>
/// 协议管理器实现
/// </summary>
public class ProtocolManager : IProtocolManager
{
    private readonly Dictionary<ProtocolFormat, IProtocolParser> _parsers;

    /// <summary>
    /// 构造函数
    /// </summary>
    public ProtocolManager()
    {
        _parsers = new Dictionary<ProtocolFormat, IProtocolParser>();
        
        // 注册默认解析器
        RegisterParser(new ParameterIndexParser());
        RegisterParser(new CustomDataParser());
    }

    /// <summary>
    /// 构建数据帧
    /// </summary>
    /// <param name="sourceAddress">源地址</param>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="commandCode">命令码</param>
    /// <param name="data">数据</param>
    /// <returns>完整数据帧</returns>
    public byte[] BuildFrame(byte sourceAddress, byte targetAddress, byte commandCode, byte[] data)
    {
        if (data == null)
            data = Array.Empty<byte>();

        if (data.Length > ProtocolConstants.MAX_DATA_LENGTH)
            throw new ArgumentException($"数据长度超过最大限制: {data.Length} > {ProtocolConstants.MAX_DATA_LENGTH}");

        // 计算帧长度：头码(1) + 源地址(1) + 目标地址(1) + 命令码(1) + 长度(1) + 数据(N) + CRC(2)
        var frameLength = 5 + data.Length + 2;
        var frame = new byte[frameLength];

        // 构建帧结构
        frame[0] = ProtocolConstants.FRAME_HEADER;  // 头码
        frame[1] = sourceAddress;                   // 源地址
        frame[2] = targetAddress;                   // 目标地址
        frame[3] = commandCode;                     // 命令码
        frame[4] = (byte)(frameLength - 2);         // 长度（不包括头码和CRC）

        // 复制数据
        if (data.Length > 0)
        {
            Array.Copy(data, 0, frame, 5, data.Length);
        }

        // 计算并添加CRC16
        var crc = CalculateCRC16(frame, 1, frameLength - 3);
        frame[frameLength - 2] = (byte)(crc >> 8);   // CRC高字节
        frame[frameLength - 1] = (byte)(crc & 0xFF); // CRC低字节

        return frame;
    }

    /// <summary>
    /// 构建参数读取帧
    /// </summary>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="parameterIndex">参数索引</param>
    /// <param name="count">参数个数</param>
    /// <returns>参数读取帧</returns>
    public byte[] BuildParameterReadFrame(byte targetAddress, byte parameterIndex, byte count)
    {
        if (count == 0 || count > ProtocolConstants.MAX_PARAMETER_COUNT)
            throw new ArgumentException($"参数个数无效: {count}");

        var data = new byte[] { parameterIndex, count };
        return BuildFrame(ProtocolConstants.MASTER_ADDRESS, targetAddress, 
                         ProtocolConstants.CMD_PARAMETER_READ, data);
    }

    /// <summary>
    /// 构建控制命令帧
    /// </summary>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="controlData">控制数据</param>
    /// <returns>控制命令帧</returns>
    public byte[] BuildControlFrame(byte targetAddress, byte[] controlData)
    {
        if (controlData == null || controlData.Length == 0)
            throw new ArgumentException("控制数据不能为空");

        return BuildFrame(ProtocolConstants.MASTER_ADDRESS, targetAddress, 
                         ProtocolConstants.CMD_CONTROL_BASE, controlData);
    }

    /// <summary>
    /// 构建心跳帧
    /// </summary>
    /// <param name="targetAddress">目标地址</param>
    /// <returns>心跳帧</returns>
    public byte[] BuildHeartbeatFrame(byte targetAddress)
    {
        var data = new byte[] { 0x00 }; // 心跳数据
        return BuildFrame(ProtocolConstants.MASTER_ADDRESS, targetAddress, 
                         ProtocolConstants.CMD_HEARTBEAT, data);
    }

    /// <summary>
    /// 解析数据帧
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <returns>解析结果</returns>
    public ParseResult ParseFrame(byte[] rawData)
    {
        if (rawData == null || rawData.Length == 0)
        {
            return new ParseResult
            {
                IsValid = false,
                ErrorMessage = "原始数据为空"
            };
        }

        // 检测协议格式
        var format = DetectFormat(rawData);
        if (format == ProtocolFormat.ParameterIndex || format == ProtocolFormat.CustomData)
        {
            var parser = GetParser(format);
            if (parser != null)
            {
                return parser.ParseFrame(rawData);
            }
        }

        // 如果无法识别格式，返回基本解析结果
        return new ParseResult
        {
            IsValid = false,
            ErrorMessage = $"无法识别的协议格式",
            RawData = rawData,
            Timestamp = DateTimeOffset.UtcNow
        };
    }

    /// <summary>
    /// 验证数据帧
    /// </summary>
    /// <param name="frame">数据帧</param>
    /// <returns>是否有效</returns>
    public bool ValidateFrame(byte[] frame)
    {
        if (frame == null || frame.Length < ProtocolConstants.MIN_FRAME_LENGTH)
            return false;

        // 检查帧头
        if (frame[0] != ProtocolConstants.FRAME_HEADER)
            return false;

        // 检查长度字段
        var declaredLength = frame[4];
        var expectedFrameLength = declaredLength + 2; // 长度字段值 + 头码 + CRC
        if (frame.Length != expectedFrameLength)
            return false;

        // 验证CRC
        var calculatedCrc = CalculateCRC16(frame, 1, frame.Length - 3);
        var frameCrc = (ushort)((frame[frame.Length - 2] << 8) | frame[frame.Length - 1]);

        return calculatedCrc == frameCrc;
    }

    /// <summary>
    /// 计算CRC16校验码
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="start">起始位置</param>
    /// <param name="length">长度</param>
    /// <returns>CRC16值</returns>
    public ushort CalculateCRC16(byte[] data, int start, int length)
    {
        if (data == null || start < 0 || length <= 0 || start + length > data.Length)
            throw new ArgumentException("CRC计算参数无效");

        ushort crc = ProtocolConstants.CRC16_INITIAL_VALUE;

        for (int i = start; i < start + length; i++)
        {
            crc ^= data[i];
            for (int j = 0; j < 8; j++)
            {
                if ((crc & 0x0001) != 0)
                {
                    crc >>= 1;
                    crc ^= ProtocolConstants.CRC16_POLYNOMIAL;
                }
                else
                {
                    crc >>= 1;
                }
            }
        }

        return crc;
    }

    /// <summary>
    /// 检测协议格式
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <returns>协议格式</returns>
    public ProtocolFormat DetectFormat(byte[] rawData)
    {
        if (rawData == null || rawData.Length < ProtocolConstants.MIN_FRAME_LENGTH)
            return ProtocolFormat.ParameterIndex; // 默认格式

        // 检查帧头
        if (rawData[0] != ProtocolConstants.FRAME_HEADER)
            return ProtocolFormat.ParameterIndex; // 默认格式

        var commandCode = rawData[3];

        // 根据命令码判断协议格式
        return commandCode switch
        {
            ProtocolConstants.CMD_PARAMETER_READ => ProtocolFormat.ParameterIndex,
            ProtocolConstants.CMD_PARAMETER_WRITE => ProtocolFormat.ParameterIndex,
            ProtocolConstants.CMD_SYSTEM_STATUS => ProtocolFormat.CustomData,
            ProtocolConstants.CMD_DEVICE_INFO => ProtocolFormat.CustomData,
            ProtocolConstants.CMD_HEARTBEAT => ProtocolFormat.CustomData,
            ProtocolConstants.CMD_DEVICE_DISCOVERY => ProtocolFormat.CustomData,
            >= ProtocolConstants.CMD_CONTROL_BASE => ProtocolFormat.CustomData,
            _ => ProtocolFormat.ParameterIndex // 默认格式
        };
    }

    /// <summary>
    /// 获取协议解析器
    /// </summary>
    /// <param name="format">协议格式</param>
    /// <returns>协议解析器</returns>
    public IProtocolParser GetParser(ProtocolFormat format)
    {
        _parsers.TryGetValue(format, out var parser);
        return parser!;
    }

    /// <summary>
    /// 注册协议解析器
    /// </summary>
    /// <param name="parser">协议解析器</param>
    public void RegisterParser(IProtocolParser parser)
    {
        if (parser == null)
            throw new ArgumentNullException(nameof(parser));

        _parsers[parser.SupportedFormat] = parser;
    }
}
