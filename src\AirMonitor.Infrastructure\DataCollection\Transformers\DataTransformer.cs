using AirMonitor.Core.Entities.Devices;
using AirMonitor.Core.Entities.Monitoring;
using AirMonitor.Core.Entities.Parameters;
using AirMonitor.Core.Enums;
using AirMonitor.Infrastructure.DataCollection.Interfaces;
using AirMonitor.Infrastructure.DataCollection.Models;
using Microsoft.Extensions.Logging;

namespace AirMonitor.Infrastructure.DataCollection.Transformers;

/// <summary>
/// 数据转换器
/// </summary>
public class DataTransformer : IDataTransformer
{
    private readonly ILogger<DataTransformer> _logger;

    public DataTransformer(ILogger<DataTransformer> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 转换数据
    /// </summary>
    /// <param name="processedData">处理后的数据</param>
    /// <param name="device">设备信息</param>
    /// <param name="parameterDefinition">参数定义</param>
    /// <returns>监控数据</returns>
    public async Task<object> TransformAsync(ProcessedData processedData, object device, object parameterDefinition)
    {
        await Task.CompletedTask;

        if (device is not Device deviceEntity)
            throw new ArgumentException("设备信息类型无效", nameof(device));

        if (parameterDefinition is not ParameterDefinition paramDef)
            throw new ArgumentException("参数定义类型无效", nameof(parameterDefinition));

        try
        {
            var monitoringData = new MonitoringData
            {
                DeviceId = deviceEntity.Id,
                ParameterIndex = processedData.ParameterIndex,
                Timestamp = processedData.Timestamp,
                Quality = processedData.Quality,
                RawValue = processedData.RawValue,
                CreatedAt = DateTimeOffset.UtcNow
            };

            // 根据参数值格式转换值
            switch (processedData.ValueFormat)
            {
                case ParameterValueFormat.SingleValue:
                    monitoringData.NumericValue = ConvertToNumeric(processedData.ParsedValue, parameterDefinition);
                    break;

                case ParameterValueFormat.BitField:
                case ParameterValueFormat.EnumValue:
                    monitoringData.StringValue = ConvertToString(processedData.ParsedValue, parameterDefinition);
                    monitoringData.NumericValue = ConvertToNumeric(processedData.ParsedValue, parameterDefinition);
                    break;

                case ParameterValueFormat.ByteCombination:
                case ParameterValueFormat.MixedFormat:
                    monitoringData.StringValue = ConvertToString(processedData.ParsedValue, parameterDefinition);
                    break;

                default:
                    _logger.LogWarning("未知的参数值格式: {ValueFormat}", processedData.ValueFormat);
                    monitoringData.StringValue = processedData.ParsedValue?.ToString();
                    break;
            }

            // 设置单位（如果MonitoringData有Unit属性的话）
            // monitoringData.Unit = paramDef.Unit;

            return monitoringData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换数据失败: 设备={DeviceId}, 参数={ParameterId}", 
                           deviceEntity.Id, paramDef.Id);
            throw;
        }
    }

    /// <summary>
    /// 批量转换数据
    /// </summary>
    /// <param name="processedDataList">处理后的数据集合</param>
    /// <param name="device">设备信息</param>
    /// <param name="parameterDefinitions">参数定义集合</param>
    /// <returns>监控数据集合</returns>
    public async Task<IEnumerable<object>> TransformBatchAsync(IEnumerable<ProcessedData> processedDataList, 
        object device, IEnumerable<object> parameterDefinitions)
    {
        var results = new List<object>();
        var paramDefDict = parameterDefinitions
            .Cast<ParameterDefinition>()
            .ToDictionary(p => p.Id);

        foreach (var processedData in processedDataList)
        {
            try
            {
                if (processedData.Metadata.TryGetValue("ParameterDefinitionId", out var paramDefIdObj) &&
                    paramDefIdObj is int paramDefId &&
                    paramDefDict.TryGetValue(paramDefId, out var paramDef))
                {
                    var monitoringData = await TransformAsync(processedData, device, paramDef);
                    results.Add(monitoringData);
                }
                else
                {
                    _logger.LogWarning("无法找到参数定义: ParameterDefinitionId={ParameterDefinitionId}", 
                                     paramDefIdObj);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量转换中单条数据转换失败");
            }
        }

        return results;
    }

    /// <summary>
    /// 转换为数值
    /// </summary>
    /// <param name="value">值</param>
    /// <param name="parameterDefinition">参数定义</param>
    /// <returns>数值</returns>
    public decimal? ConvertToNumeric(object value, object parameterDefinition)
    {
        if (value == null)
            return null;

        if (parameterDefinition is not ParameterDefinition paramDef)
            return null;

        try
        {
            // 直接转换数值类型
            if (value is decimal decimalValue)
                return ApplyScaleAndOffset(decimalValue, paramDef);

            if (value is double doubleValue)
                return ApplyScaleAndOffset((decimal)doubleValue, paramDef);

            if (value is float floatValue)
                return ApplyScaleAndOffset((decimal)floatValue, paramDef);

            if (value is int intValue)
                return ApplyScaleAndOffset(intValue, paramDef);

            if (value is long longValue)
                return ApplyScaleAndOffset(longValue, paramDef);

            if (value is ushort ushortValue)
                return ApplyScaleAndOffset(ushortValue, paramDef);

            // 尝试解析字符串
            if (value is string stringValue && decimal.TryParse(stringValue, out var parsedValue))
                return ApplyScaleAndOffset(parsedValue, paramDef);

            // 布尔值转换为0/1
            if (value is bool boolValue)
                return boolValue ? 1 : 0;

            _logger.LogWarning("无法转换为数值: {Value} ({Type})", value, value.GetType().Name);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数值转换失败: {Value}", value);
            return null;
        }
    }

    /// <summary>
    /// 转换为字符串
    /// </summary>
    /// <param name="value">值</param>
    /// <param name="parameterDefinition">参数定义</param>
    /// <returns>字符串</returns>
    public string? ConvertToString(object value, object parameterDefinition)
    {
        if (value == null)
            return null;

        if (parameterDefinition is not ParameterDefinition paramDef)
            return value.ToString();

        try
        {
            // 如果是枚举类型，尝试转换为枚举文本
            // 简化实现，直接转换为字符串
            if (value is ushort || value is int)
            {
                return ConvertEnumToString(value, paramDef);
            }

            // 数值类型格式化
            if (value is decimal decimalValue)
            {
                return decimalValue.ToString("F2");
            }

            if (value is double doubleValue)
            {
                return doubleValue.ToString("F2");
            }

            // 布尔值转换
            if (value is bool boolValue)
            {
                return boolValue ? "是" : "否";
            }

            return value.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "字符串转换失败: {Value}", value);
            return value.ToString();
        }
    }

    /// <summary>
    /// 转换为布尔值
    /// </summary>
    /// <param name="value">值</param>
    /// <param name="parameterDefinition">参数定义</param>
    /// <returns>布尔值</returns>
    public bool? ConvertToBoolean(object value, object parameterDefinition)
    {
        if (value == null)
            return null;

        try
        {
            // 直接布尔值
            if (value is bool boolValue)
                return boolValue;

            // 数值转换（0为false，非0为true）
            if (value is decimal decimalValue)
                return decimalValue != 0;

            if (value is double doubleValue)
                return doubleValue != 0;

            if (value is int intValue)
                return intValue != 0;

            if (value is ushort ushortValue)
                return ushortValue != 0;

            // 字符串转换
            if (value is string stringValue)
            {
                if (bool.TryParse(stringValue, out var parsedBool))
                    return parsedBool;

                // 尝试数值转换
                if (decimal.TryParse(stringValue, out var parsedDecimal))
                    return parsedDecimal != 0;

                // 文本判断
                var lowerValue = stringValue.ToLowerInvariant();
                return lowerValue is "true" or "yes" or "是" or "开" or "1" or "on";
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "布尔值转换失败: {Value}", value);
            return null;
        }
    }

    /// <summary>
    /// 应用缩放和偏移
    /// </summary>
    /// <param name="value">原始值</param>
    /// <param name="paramDef">参数定义</param>
    /// <returns>转换后的值</returns>
    private decimal ApplyScaleAndOffset(decimal value, ParameterDefinition paramDef)
    {
        // 简化实现，直接返回原值
        // 如果需要缩放和偏移，可以在这里添加逻辑
        return value;
    }

    /// <summary>
    /// 枚举值转换为字符串
    /// </summary>
    /// <param name="value">值</param>
    /// <param name="paramDef">参数定义</param>
    /// <returns>枚举文本</returns>
    private string ConvertEnumToString(object value, ParameterDefinition paramDef)
    {
        // 这里可以根据参数定义中的枚举映射来转换
        // 简化实现，直接返回值的字符串表示
        if (value is ushort ushortValue)
        {
            // 根据参数索引返回预定义的枚举文本
            return paramDef.Index switch
            {
                0x50 => ushortValue switch // 运行状态
                {
                    0 => "停止",
                    1 => "运行",
                    2 => "故障",
                    _ => $"未知({ushortValue})"
                },
                0x51 => ushortValue switch // 运行模式
                {
                    0 => "制冷",
                    1 => "制热",
                    2 => "除湿",
                    3 => "送风",
                    4 => "自动",
                    _ => $"未知({ushortValue})"
                },
                _ => value.ToString() ?? ""
            };
        }

        return value.ToString() ?? "";
    }
}
