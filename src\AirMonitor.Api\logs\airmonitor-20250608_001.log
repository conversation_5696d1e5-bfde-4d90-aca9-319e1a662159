2025-06-08 11:59:48.493 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-08 11:59:48.775 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:59:48.780 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:59:48.866 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-08 11:59:48.924 +08:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-08 11:59:48.927 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-06-08 11:59:48.932 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-06-08 03:59:48.9298754+00:00');
SELECT changes();
2025-06-08 11:59:48.959 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-06-08 11:59:48.966 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-06-08 11:59:48.968 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-08 11:59:48.973 +08:00 [INF] No migrations were applied. The database is already up to date.
2025-06-08 11:59:48.977 +08:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-06-08 11:59:48.979 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-08 11:59:49.108 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "AM_ParameterTemplates" AS "a")
2025-06-08 11:59:49.116 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "AM_Users" AS "a"
    WHERE "a"."IsActive")
2025-06-08 11:59:49.154 +08:00 [INF] AirMonitor API 服务启动成功，监听地址: http://localhost:5236
2025-06-08 11:59:49.181 +08:00 [INF] Now listening on: http://localhost:5236
2025-06-08 11:59:49.182 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-08 11:59:49.182 +08:00 [INF] Hosting environment: Development
2025-06-08 11:59:49.182 +08:00 [INF] Content root path: D:\Project\08 AirMonitor\src\AirMonitor.Api
2025-06-08 12:00:06.566 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/index.html - null null
2025-06-08 12:00:06.624 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/index.html - 200 null text/html;charset=utf-8 58.7754ms
2025-06-08 12:00:06.713 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/swagger/v1/swagger.json - null null
2025-06-08 12:00:06.776 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 63.4043ms
2025-06-08 12:00:31.871 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/api/System/health - null null
2025-06-08 12:00:31.874 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-08 12:00:31.882 +08:00 [INF] Executing endpoint 'AirMonitor.Api.Controllers.SystemController.HealthCheck (AirMonitor.Api)'
2025-06-08 12:00:31.889 +08:00 [INF] Route matched with {action = "HealthCheck", controller = "System"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult HealthCheck() on controller AirMonitor.Api.Controllers.SystemController (AirMonitor.Api).
2025-06-08 12:00:31.896 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType6`4[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.TimeSpan, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType7`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], AirMonitor.Api, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-08 12:00:31.911 +08:00 [INF] Executed action AirMonitor.Api.Controllers.SystemController.HealthCheck (AirMonitor.Api) in 20.1432ms
2025-06-08 12:00:31.912 +08:00 [INF] Executed endpoint 'AirMonitor.Api.Controllers.SystemController.HealthCheck (AirMonitor.Api)'
2025-06-08 12:00:31.912 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/api/System/health - 200 null application/json; charset=utf-8 41.1553ms
2025-06-08 12:00:45.364 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/api/status - null null
2025-06-08 12:00:45.368 +08:00 [INF] Executing endpoint 'HTTP: GET /api/status'
2025-06-08 12:00:45.376 +08:00 [INF] Setting HTTP status code 200.
2025-06-08 12:00:45.379 +08:00 [INF] Writing value of type '<>f__AnonymousType10`5' as Json.
2025-06-08 12:00:45.381 +08:00 [INF] Executed endpoint 'HTTP: GET /api/status'
2025-06-08 12:00:45.382 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/api/status - 200 null application/json; charset=utf-8 17.1196ms
2025-06-08 12:01:09.482 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/api/devices - null null
2025-06-08 12:01:09.483 +08:00 [INF] Executing endpoint 'AirMonitor.Api.Controllers.DevicesController.GetDevices (AirMonitor.Api)'
2025-06-08 12:01:09.487 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Devices"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[AirMonitor.Core.Entities.Devices.Device]]] GetDevices() on controller AirMonitor.Api.Controllers.DevicesController (AirMonitor.Api).
2025-06-08 12:01:09.516 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."Description", "a"."DeviceAddress", "a"."DeviceType", "a"."FirmwareVersion", "a"."IsOnline", "a"."LastCommunication", "a"."Location", "a"."ModelNumber", "a"."SerialNumber", "a"."Status", "a"."UpdatedAt"
FROM "AM_Devices" AS "a"
2025-06-08 12:01:09.518 +08:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[AirMonitor.Core.Entities.Devices.Device, AirMonitor.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-08 12:01:09.538 +08:00 [INF] Executed action AirMonitor.Api.Controllers.DevicesController.GetDevices (AirMonitor.Api) in 50.6048ms
2025-06-08 12:01:09.539 +08:00 [INF] Executed endpoint 'AirMonitor.Api.Controllers.DevicesController.GetDevices (AirMonitor.Api)'
2025-06-08 12:01:09.541 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/api/devices - 200 null application/json; charset=utf-8 58.6838ms
2025-06-08 12:01:32.492 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:5236/api/devices - application/json 307
2025-06-08 12:01:32.494 +08:00 [INF] Executing endpoint 'AirMonitor.Api.Controllers.DevicesController.CreateDevice (AirMonitor.Api)'
2025-06-08 12:01:32.498 +08:00 [INF] Route matched with {action = "CreateDevice", controller = "Devices"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[AirMonitor.Core.Entities.Devices.Device]] CreateDevice(AirMonitor.Core.Entities.Devices.Device) on controller AirMonitor.Api.Controllers.DevicesController (AirMonitor.Api).
2025-06-08 12:01:32.559 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@__deviceAddress_0='241'], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."Description", "a"."DeviceAddress", "a"."DeviceType", "a"."FirmwareVersion", "a"."IsOnline", "a"."LastCommunication", "a"."Location", "a"."ModelNumber", "a"."SerialNumber", "a"."Status", "a"."UpdatedAt"
FROM "AM_Devices" AS "a"
WHERE "a"."DeviceAddress" = @__deviceAddress_0
LIMIT 1
2025-06-08 12:01:32.628 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='2025-06-08T04:01:32.5612401+00:00', @p1='Main outdoor unit for Building A' (Size = 32), @p2='241', @p3='1', @p4='V1.0.0' (Nullable = false) (Size = 6), @p5='True', @p6='2025-01-17T12:00:00.0000000+00:00', @p7='Building A - Roof' (Size = 17), @p8='AC-1000' (Nullable = false) (Size = 7), @p9='AC-OUT-001' (Size = 10), @p10='1', @p11='2025-06-08T04:01:32.5612826+00:00'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_Devices" ("CreatedAt", "Description", "DeviceAddress", "DeviceType", "FirmwareVersion", "IsOnline", "LastCommunication", "Location", "ModelNumber", "SerialNumber", "Status", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11)
RETURNING "Id";
2025-06-08 12:01:32.640 +08:00 [INF] Executing CreatedAtActionResult, writing value of type 'AirMonitor.Core.Entities.Devices.Device'.
2025-06-08 12:01:32.648 +08:00 [INF] Executed action AirMonitor.Api.Controllers.DevicesController.CreateDevice (AirMonitor.Api) in 149.2041ms
2025-06-08 12:01:32.649 +08:00 [INF] Executed endpoint 'AirMonitor.Api.Controllers.DevicesController.CreateDevice (AirMonitor.Api)'
2025-06-08 12:01:32.649 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:5236/api/devices - 201 null application/json; charset=utf-8 157.1224ms
2025-06-08 12:01:45.856 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/api/devices - null null
2025-06-08 12:01:45.857 +08:00 [INF] Executing endpoint 'AirMonitor.Api.Controllers.DevicesController.GetDevices (AirMonitor.Api)'
2025-06-08 12:01:45.858 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Devices"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[AirMonitor.Core.Entities.Devices.Device]]] GetDevices() on controller AirMonitor.Api.Controllers.DevicesController (AirMonitor.Api).
2025-06-08 12:01:45.860 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."Description", "a"."DeviceAddress", "a"."DeviceType", "a"."FirmwareVersion", "a"."IsOnline", "a"."LastCommunication", "a"."Location", "a"."ModelNumber", "a"."SerialNumber", "a"."Status", "a"."UpdatedAt"
FROM "AM_Devices" AS "a"
2025-06-08 12:01:45.863 +08:00 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[AirMonitor.Core.Entities.Devices.Device, AirMonitor.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-08 12:01:45.864 +08:00 [INF] Executed action AirMonitor.Api.Controllers.DevicesController.GetDevices (AirMonitor.Api) in 5.1891ms
2025-06-08 12:01:45.864 +08:00 [INF] Executed endpoint 'AirMonitor.Api.Controllers.DevicesController.GetDevices (AirMonitor.Api)'
2025-06-08 12:01:45.865 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/api/devices - 200 null application/json; charset=utf-8 8.788ms
2025-06-08 12:01:57.474 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/api/devices/address/241 - null null
2025-06-08 12:01:57.475 +08:00 [INF] Executing endpoint 'AirMonitor.Api.Controllers.DevicesController.GetDeviceByAddress (AirMonitor.Api)'
2025-06-08 12:01:57.478 +08:00 [INF] Route matched with {action = "GetDeviceByAddress", controller = "Devices"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[AirMonitor.Core.Entities.Devices.Device]] GetDeviceByAddress(Byte) on controller AirMonitor.Api.Controllers.DevicesController (AirMonitor.Api).
2025-06-08 12:01:57.481 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__deviceAddress_0='241'], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."Description", "a"."DeviceAddress", "a"."DeviceType", "a"."FirmwareVersion", "a"."IsOnline", "a"."LastCommunication", "a"."Location", "a"."ModelNumber", "a"."SerialNumber", "a"."Status", "a"."UpdatedAt"
FROM "AM_Devices" AS "a"
WHERE "a"."DeviceAddress" = @__deviceAddress_0
LIMIT 1
2025-06-08 12:01:57.483 +08:00 [INF] Executing OkObjectResult, writing value of type 'AirMonitor.Core.Entities.Devices.Device'.
2025-06-08 12:01:57.483 +08:00 [INF] Executed action AirMonitor.Api.Controllers.DevicesController.GetDeviceByAddress (AirMonitor.Api) in 3.9645ms
2025-06-08 12:01:57.484 +08:00 [INF] Executed endpoint 'AirMonitor.Api.Controllers.DevicesController.GetDeviceByAddress (AirMonitor.Api)'
2025-06-08 12:01:57.484 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/api/devices/address/241 - 200 null application/json; charset=utf-8 10.7129ms
2025-06-08 12:02:16.943 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/api/Devices/address/241 - null null
2025-06-08 12:02:16.945 +08:00 [INF] Executing endpoint 'AirMonitor.Api.Controllers.DevicesController.GetDeviceByAddress (AirMonitor.Api)'
2025-06-08 12:02:16.946 +08:00 [INF] Route matched with {action = "GetDeviceByAddress", controller = "Devices"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[AirMonitor.Core.Entities.Devices.Device]] GetDeviceByAddress(Byte) on controller AirMonitor.Api.Controllers.DevicesController (AirMonitor.Api).
2025-06-08 12:02:16.948 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@__deviceAddress_0='241'], CommandType='"Text"', CommandTimeout='30']
SELECT "a"."Id", "a"."CreatedAt", "a"."Description", "a"."DeviceAddress", "a"."DeviceType", "a"."FirmwareVersion", "a"."IsOnline", "a"."LastCommunication", "a"."Location", "a"."ModelNumber", "a"."SerialNumber", "a"."Status", "a"."UpdatedAt"
FROM "AM_Devices" AS "a"
WHERE "a"."DeviceAddress" = @__deviceAddress_0
LIMIT 1
2025-06-08 12:02:16.949 +08:00 [INF] Executing OkObjectResult, writing value of type 'AirMonitor.Core.Entities.Devices.Device'.
2025-06-08 12:02:16.950 +08:00 [INF] Executed action AirMonitor.Api.Controllers.DevicesController.GetDeviceByAddress (AirMonitor.Api) in 2.98ms
2025-06-08 12:02:16.951 +08:00 [INF] Executed endpoint 'AirMonitor.Api.Controllers.DevicesController.GetDeviceByAddress (AirMonitor.Api)'
2025-06-08 12:02:16.951 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/api/Devices/address/241 - 200 null application/json; charset=utf-8 8.8587ms
