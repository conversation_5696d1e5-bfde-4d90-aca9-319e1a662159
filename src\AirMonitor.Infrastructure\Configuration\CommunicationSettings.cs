namespace AirMonitor.Infrastructure.Configuration;

/// <summary>
/// 通信设置配置
/// </summary>
public class CommunicationSettings
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "Communication";

    /// <summary>
    /// 默认轮询间隔（毫秒）
    /// </summary>
    public int DefaultPollingInterval { get; set; } = 5000;

    /// <summary>
    /// 设备发现超时时间（毫秒）
    /// </summary>
    public int DeviceDiscoveryTimeout { get; set; } = 10000;

    /// <summary>
    /// 最大并发操作数
    /// </summary>
    public int MaxConcurrentOperations { get; set; } = 10;

    /// <summary>
    /// 数据缓存大小
    /// </summary>
    public int DataCacheSize { get; set; } = 1000;

    /// <summary>
    /// 批量处理大小
    /// </summary>
    public int BatchProcessingSize { get; set; } = 100;

    /// <summary>
    /// 启用数据验证
    /// </summary>
    public bool EnableDataValidation { get; set; } = true;

    /// <summary>
    /// 启用性能日志
    /// </summary>
    public bool EnablePerformanceLogging { get; set; } = false;

    /// <summary>
    /// 设备离线超时时间（毫秒）
    /// </summary>
    public int DeviceOfflineTimeout { get; set; } = 120000; // 2分钟

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 重试间隔（毫秒）
    /// </summary>
    public int RetryInterval { get; set; } = 1000;

    /// <summary>
    /// 启用自动重连
    /// </summary>
    public bool EnableAutoReconnect { get; set; } = true;

    /// <summary>
    /// 心跳间隔（毫秒）
    /// </summary>
    public int HeartbeatInterval { get; set; } = 30000;

    /// <summary>
    /// 数据接收缓冲区大小
    /// </summary>
    public int ReceiveBufferSize { get; set; } = 4096;

    /// <summary>
    /// 数据发送缓冲区大小
    /// </summary>
    public int SendBufferSize { get; set; } = 4096;

    /// <summary>
    /// 启用数据压缩
    /// </summary>
    public bool EnableDataCompression { get; set; } = false;

    /// <summary>
    /// 启用数据加密
    /// </summary>
    public bool EnableDataEncryption { get; set; } = false;

    /// <summary>
    /// 最大帧长度
    /// </summary>
    public int MaxFrameLength { get; set; } = 512;

    /// <summary>
    /// 帧间隔时间（毫秒）
    /// </summary>
    public int FrameInterval { get; set; } = 10;

    /// <summary>
    /// 启用流量控制
    /// </summary>
    public bool EnableFlowControl { get; set; } = true;

    /// <summary>
    /// 流量控制阈值
    /// </summary>
    public int FlowControlThreshold { get; set; } = 80; // 百分比

    /// <summary>
    /// 启用统计信息收集
    /// </summary>
    public bool EnableStatistics { get; set; } = true;

    /// <summary>
    /// 统计信息保留时间（小时）
    /// </summary>
    public int StatisticsRetentionHours { get; set; } = 24;
}
