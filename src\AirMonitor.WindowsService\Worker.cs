using AirMonitor.Protocols.Interfaces;
using AirMonitor.Protocols.Models;

namespace AirMonitor.WindowsService;

/// <summary>
/// AirMonitor Windows 服务工作器
/// </summary>
public class Worker : BackgroundService
{
    private readonly ILogger<Worker> _logger;
    private readonly ISerialCommunicationService _communicationService;
    private readonly IDeviceManager _deviceManager;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="communicationService">串口通信服务</param>
    /// <param name="deviceManager">设备管理器</param>
    public Worker(
        ILogger<Worker> logger,
        ISerialCommunicationService communicationService,
        IDeviceManager deviceManager)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
        _deviceManager = deviceManager ?? throw new ArgumentNullException(nameof(deviceManager));

        // 订阅事件
        _communicationService.DeviceDataReceived += OnDeviceDataReceived;
        _communicationService.DeviceStatusChanged += OnDeviceStatusChanged;
        _communicationService.CommunicationError += OnCommunicationError;
        _deviceManager.DeviceDiscovered += OnDeviceDiscovered;
        _deviceManager.DeviceOffline += OnDeviceOffline;
    }

    /// <summary>
    /// 执行服务主逻辑
    /// </summary>
    /// <param name="stoppingToken">停止令牌</param>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("AirMonitor Windows Service 开始执行");

        try
        {
            // 启动串口通信服务
            var started = await _communicationService.StartAsync(stoppingToken);
            if (!started)
            {
                _logger.LogError("串口通信服务启动失败");
                return;
            }

            _logger.LogInformation("串口通信服务启动成功");

            // 主循环 - 保持服务运行
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // 检查连接状态
                    var isConnected = await _communicationService.IsConnectedAsync();
                    if (!isConnected)
                    {
                        _logger.LogWarning("串口连接已断开，尝试重新连接...");
                        await _communicationService.StartAsync(stoppingToken);
                    }

                    // 获取设备统计信息
                    var devices = await _deviceManager.GetAllDevicesAsync(stoppingToken);
                    var onlineDevices = 0;
                    var offlineDevices = 0;

                    foreach (var device in devices)
                    {
                        var isOnline = await _deviceManager.IsDeviceOnlineAsync(device.Address, stoppingToken);
                        if (isOnline)
                            onlineDevices++;
                        else
                            offlineDevices++;
                    }

                    _logger.LogDebug("设备状态统计: 在线={OnlineCount}, 离线={OfflineCount}, 总计={TotalCount}",
                                   onlineDevices, offlineDevices, devices.Count());

                    // 等待下一次检查
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // 正常取消，退出循环
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "服务执行过程中发生异常");
                    await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("AirMonitor Windows Service 收到停止信号");
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "AirMonitor Windows Service 执行失败");
            throw;
        }
        finally
        {
            // 停止串口通信服务
            try
            {
                await _communicationService.StopAsync();
                _logger.LogInformation("串口通信服务已停止");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止串口通信服务时发生异常");
            }
        }

        _logger.LogInformation("AirMonitor Windows Service 执行完成");
    }

    /// <summary>
    /// 设备数据接收事件处理器
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnDeviceDataReceived(object? sender, DeviceDataReceivedEventArgs e)
    {
        _logger.LogDebug("接收到设备数据: 地址=0x{Address:X2}, 命令码=0x{CommandCode:X2}, 格式={Format}",
                       e.DeviceAddress, e.ParseResult.CommandCode, e.ParseResult.Format);

        // 这里可以添加数据处理逻辑，例如：
        // 1. 将数据发送到云端API
        // 2. 存储到本地数据库
        // 3. 触发告警处理
        // 4. 更新实时数据缓存
    }

    /// <summary>
    /// 设备状态变化事件处理器
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnDeviceStatusChanged(object? sender, DeviceStatusChangedEventArgs e)
    {
        _logger.LogInformation("设备状态变化: 地址=0x{Address:X2}, {OldStatus} -> {NewStatus}",
                             e.DeviceAddress, e.OldStatus, e.NewStatus);

        // 这里可以添加状态变化处理逻辑，例如：
        // 1. 发送状态变化通知
        // 2. 更新设备状态缓存
        // 3. 触发告警或恢复处理
    }

    /// <summary>
    /// 通信错误事件处理器
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnCommunicationError(object? sender, CommunicationErrorEventArgs e)
    {
        _logger.LogWarning("通信错误: 地址=0x{Address:X2}, 错误类型={ErrorType}, 错误信息={ErrorMessage}",
                         e.DeviceAddress, e.ErrorType, e.ErrorMessage);

        // 这里可以添加错误处理逻辑，例如：
        // 1. 记录错误统计
        // 2. 触发告警
        // 3. 尝试恢复连接
    }

    /// <summary>
    /// 设备发现事件处理器
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="device">设备信息</param>
    private void OnDeviceDiscovered(object? sender, DeviceInfo device)
    {
        _logger.LogInformation("发现新设备: 地址=0x{Address:X2}, 类型={DeviceType}, 序列号={SerialNumber}",
                             device.Address, device.DeviceType, device.SerialNumber);

        // 这里可以添加设备发现处理逻辑，例如：
        // 1. 注册设备到云端
        // 2. 初始化设备配置
        // 3. 发送设备发现通知
    }

    /// <summary>
    /// 设备离线事件处理器
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="device">设备信息</param>
    private void OnDeviceOffline(object? sender, DeviceInfo device)
    {
        _logger.LogWarning("设备离线: 地址=0x{Address:X2}, 类型={DeviceType}",
                         device.Address, device.DeviceType);

        // 这里可以添加设备离线处理逻辑，例如：
        // 1. 发送离线告警
        // 2. 更新设备状态
        // 3. 停止相关监控任务
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public override void Dispose()
    {
        // 取消事件订阅
        if (_communicationService != null)
        {
            _communicationService.DeviceDataReceived -= OnDeviceDataReceived;
            _communicationService.DeviceStatusChanged -= OnDeviceStatusChanged;
            _communicationService.CommunicationError -= OnCommunicationError;
        }

        if (_deviceManager != null)
        {
            _deviceManager.DeviceDiscovered -= OnDeviceDiscovered;
            _deviceManager.DeviceOffline -= OnDeviceOffline;
        }

        base.Dispose();
    }
}
