using AirMonitor.Infrastructure.Communication;
using AirMonitor.Infrastructure.Configuration;
using AirMonitor.Protocols.Interfaces;
using AirMonitor.Protocols.Services;
using AirMonitor.WindowsService;
using Serilog;

// 创建主机构建器
var builder = Host.CreateApplicationBuilder(args);

// 配置 Serilog
builder.Services.AddSerilog((services, lc) => lc
    .ReadFrom.Configuration(builder.Configuration)
    .ReadFrom.Services(services)
    .Enrich.FromLogContext());

// 配置强类型设置
builder.Services.Configure<SerialPortSettings>(
    builder.Configuration.GetSection(SerialPortSettings.SectionName));
builder.Services.Configure<CommunicationSettings>(
    builder.Configuration.GetSection(CommunicationSettings.SectionName));

// 注册服务
builder.Services.AddSingleton<IProtocolManager, ProtocolManager>();
builder.Services.AddSingleton<ISerialPortManager, SerialPortManager>();
builder.Services.AddSingleton<IDeviceManager, DeviceManager>();
builder.Services.AddSingleton<ISerialCommunicationService, SerialCommunicationService>();

// 注册后台服务
builder.Services.AddHostedService<Worker>();

// 配置为 Windows 服务
builder.Services.AddWindowsService(options =>
{
    options.ServiceName = "AirMonitor Service";
});

var host = builder.Build();

try
{
    Log.Information("AirMonitor Windows Service 正在启动...");
    await host.RunAsync();
}
catch (Exception ex)
{
    Log.Fatal(ex, "AirMonitor Windows Service 启动失败");
    throw;
}
finally
{
    Log.CloseAndFlush();
}
