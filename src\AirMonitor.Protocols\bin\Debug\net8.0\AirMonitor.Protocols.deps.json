{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AirMonitor.Protocols/1.0.0": {"dependencies": {"AirMonitor.Core": "1.0.0"}, "runtime": {"AirMonitor.Protocols.dll": {}}}, "AirMonitor.Core/1.0.0": {"runtime": {"AirMonitor.Core.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"AirMonitor.Protocols/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AirMonitor.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}