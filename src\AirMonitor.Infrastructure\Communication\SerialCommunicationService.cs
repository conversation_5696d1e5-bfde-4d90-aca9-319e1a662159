using System.Collections.Concurrent;
using AirMonitor.Core.Enums;
using AirMonitor.Infrastructure.Configuration;
using AirMonitor.Protocols.Constants;
using AirMonitor.Protocols.Interfaces;
using AirMonitor.Protocols.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace AirMonitor.Infrastructure.Communication;

/// <summary>
/// 串口通信服务实现
/// </summary>
public class SerialCommunicationService : ISerialCommunicationService, IDisposable
{
    private readonly ILogger<SerialCommunicationService> _logger;
    private readonly SerialPortSettings _serialPortSettings;
    private readonly CommunicationSettings _communicationSettings;
    private readonly ISerialPortManager _serialPortManager;
    private readonly IProtocolManager _protocolManager;
    private readonly IDeviceManager _deviceManager;
    private readonly SemaphoreSlim _communicationSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private readonly ConcurrentDictionary<byte, int> _deviceErrorCounts;
    
    private Task? _dataReceiveTask;
    private Task? _deviceDiscoveryTask;
    private bool _disposed;

    /// <summary>
    /// 设备数据接收事件
    /// </summary>
    public event EventHandler<DeviceDataReceivedEventArgs>? DeviceDataReceived;

    /// <summary>
    /// 设备状态变化事件
    /// </summary>
    public event EventHandler<DeviceStatusChangedEventArgs>? DeviceStatusChanged;

    /// <summary>
    /// 通信错误事件
    /// </summary>
    public event EventHandler<CommunicationErrorEventArgs>? CommunicationError;

    /// <summary>
    /// 构造函数
    /// </summary>
    public SerialCommunicationService(
        ILogger<SerialCommunicationService> logger,
        IOptions<SerialPortSettings> serialPortSettings,
        IOptions<CommunicationSettings> communicationSettings,
        ISerialPortManager serialPortManager,
        IProtocolManager protocolManager,
        IDeviceManager deviceManager)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serialPortSettings = serialPortSettings?.Value ?? throw new ArgumentNullException(nameof(serialPortSettings));
        _communicationSettings = communicationSettings?.Value ?? throw new ArgumentNullException(nameof(communicationSettings));
        _serialPortManager = serialPortManager ?? throw new ArgumentNullException(nameof(serialPortManager));
        _protocolManager = protocolManager ?? throw new ArgumentNullException(nameof(protocolManager));
        _deviceManager = deviceManager ?? throw new ArgumentNullException(nameof(deviceManager));

        _communicationSemaphore = new SemaphoreSlim(_communicationSettings.MaxConcurrentOperations, 
                                                   _communicationSettings.MaxConcurrentOperations);
        _cancellationTokenSource = new CancellationTokenSource();
        _deviceErrorCounts = new ConcurrentDictionary<byte, int>();

        // 订阅事件
        _serialPortManager.DataReceived += OnDataReceived;
        _serialPortManager.ErrorOccurred += OnSerialPortError;
        _deviceManager.DeviceStatusChanged += OnDeviceStatusChanged;
    }

    /// <summary>
    /// 启动服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动是否成功</returns>
    public async Task<bool> StartAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("启动串口通信服务...");

            // 1. 初始化串口连接
            var connected = await _serialPortManager.ConnectAsync(_serialPortSettings.DefaultPortName, cancellationToken);
            if (!connected)
            {
                _logger.LogError("串口连接失败: {PortName}", _serialPortSettings.DefaultPortName);
                return false;
            }

            // 2. 启动数据接收监听
            _dataReceiveTask = Task.Run(() => DataReceiveLoop(_cancellationTokenSource.Token), 
                                      _cancellationTokenSource.Token);

            // 3. 启动设备轮询
            await _deviceManager.StartPollingAsync(_cancellationTokenSource.Token);

            // 4. 启动设备发现
            _deviceDiscoveryTask = Task.Run(() => DeviceDiscoveryLoop(_cancellationTokenSource.Token), 
                                          _cancellationTokenSource.Token);

            _logger.LogInformation("串口通信服务启动成功");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动串口通信服务失败");
            return false;
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("停止串口通信服务...");

            // 1. 取消所有任务
            _cancellationTokenSource.Cancel();

            // 2. 停止设备轮询
            await _deviceManager.StopPollingAsync();

            // 3. 等待任务完成
            var tasks = new List<Task>();
            if (_dataReceiveTask != null) tasks.Add(_dataReceiveTask);
            if (_deviceDiscoveryTask != null) tasks.Add(_deviceDiscoveryTask);

            if (tasks.Count > 0)
            {
                await Task.WhenAll(tasks);
            }

            // 4. 断开串口连接
            await _serialPortManager.DisconnectAsync(cancellationToken);

            _logger.LogInformation("串口通信服务已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止串口通信服务失败");
        }
    }

    /// <summary>
    /// 检查连接状态
    /// </summary>
    /// <returns>是否已连接</returns>
    public async Task<bool> IsConnectedAsync()
    {
        await Task.CompletedTask; // 保持异步接口一致性
        return _serialPortManager.IsConnected;
    }

    /// <summary>
    /// 发送命令
    /// </summary>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="commandCode">命令码</param>
    /// <param name="data">数据</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应数据</returns>
    public async Task<byte[]?> SendCommandAsync(byte targetAddress, byte commandCode, byte[] data, 
        TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        if (!_serialPortManager.IsConnected)
        {
            _logger.LogWarning("串口未连接，无法发送命令");
            return null;
        }

        await _communicationSemaphore.WaitAsync(cancellationToken);
        try
        {
            // 1. 构建数据帧
            var frame = _protocolManager.BuildFrame(ProtocolConstants.MASTER_ADDRESS, 
                targetAddress, commandCode, data);

            _logger.LogDebug("发送命令: 目标地址=0x{TargetAddress:X2}, 命令码=0x{CommandCode:X2}, 数据长度={DataLength}",
                           targetAddress, commandCode, data?.Length ?? 0);

            // 2. 发送数据帧
            var success = await _serialPortManager.SendAsync(frame, cancellationToken);
            if (!success)
            {
                _logger.LogWarning("发送数据帧失败: 目标地址=0x{TargetAddress:X2}", targetAddress);
                await HandleCommunicationError(targetAddress, "发送数据帧失败");
                return null;
            }

            // 3. 等待响应
            var responseTimeout = timeout ?? TimeSpan.FromMilliseconds(_serialPortSettings.ReadTimeout);
            var response = await _serialPortManager.ReceiveAsync(responseTimeout, cancellationToken);

            // 4. 验证响应
            if (response != null && _protocolManager.ValidateFrame(response))
            {
                _logger.LogDebug("收到有效响应: 长度={Length}, 目标地址=0x{TargetAddress:X2}", 
                               response.Length, targetAddress);
                
                // 重置错误计数
                _deviceErrorCounts.TryRemove(targetAddress, out _);
                return response;
            }

            _logger.LogWarning("未收到有效响应: 目标地址=0x{TargetAddress:X2}", targetAddress);
            await HandleCommunicationError(targetAddress, "未收到有效响应");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送命令异常: 目标地址=0x{TargetAddress:X2}", targetAddress);
            await HandleCommunicationError(targetAddress, ex.Message);
            return null;
        }
        finally
        {
            _communicationSemaphore.Release();
        }
    }

    /// <summary>
    /// 发送命令并解析响应
    /// </summary>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="commandCode">命令码</param>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解析结果</returns>
    public async Task<ParseResult?> SendAndParseAsync(byte targetAddress, byte commandCode, byte[] data,
        CancellationToken cancellationToken = default)
    {
        var response = await SendCommandAsync(targetAddress, commandCode, data, null, cancellationToken);
        if (response == null)
            return null;

        return _protocolManager.ParseFrame(response);
    }

    /// <summary>
    /// 发现设备
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发现的设备列表</returns>
    public async Task<IEnumerable<DeviceInfo>> DiscoverDevicesAsync(CancellationToken cancellationToken = default)
    {
        var discoveredDevices = new List<DeviceInfo>();

        _logger.LogInformation("开始设备发现...");

        try
        {
            // 扫描地址范围 0x01 到 0xFE
            for (byte address = ProtocolConstants.MIN_DEVICE_ADDRESS; 
                 address <= ProtocolConstants.MAX_DEVICE_ADDRESS; 
                 address++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                try
                {
                    // 发送设备信息查询命令
                    var response = await SendCommandAsync(address, ProtocolConstants.CMD_DEVICE_INFO, 
                                                        new byte[] { 0x00 }, 
                                                        TimeSpan.FromSeconds(2), 
                                                        cancellationToken);

                    if (response != null)
                    {
                        var parseResult = _protocolManager.ParseFrame(response);
                        if (parseResult.IsValid)
                        {
                            var device = CreateDeviceFromResponse(address, parseResult);
                            discoveredDevices.Add(device);
                            await _deviceManager.RegisterDeviceAsync(device, cancellationToken);

                            _logger.LogInformation("发现设备: 地址=0x{Address:X2}, 类型={DeviceType}",
                                                 address, device.DeviceType);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug("扫描地址 0x{Address:X2} 失败: {Error}", address, ex.Message);
                }

                // 避免过快扫描
                await Task.Delay(100, cancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("设备发现被取消");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设备发现过程中发生异常");
        }

        _logger.LogInformation("设备发现完成，共发现 {Count} 个设备", discoveredDevices.Count);
        return discoveredDevices;
    }

    /// <summary>
    /// 获取设备信息
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设备信息</returns>
    public async Task<DeviceInfo?> GetDeviceInfoAsync(byte address, CancellationToken cancellationToken = default)
    {
        return await _deviceManager.GetDeviceAsync(address, cancellationToken);
    }

    /// <summary>
    /// 数据接收循环
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task DataReceiveLoop(CancellationToken cancellationToken)
    {
        _logger.LogDebug("数据接收循环已启动");

        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                // 这里主要是保持循环运行，实际数据接收在事件处理器中处理
                await Task.Delay(100, cancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消，不记录错误
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据接收循环异常");
        }

        _logger.LogDebug("数据接收循环已停止");
    }

    /// <summary>
    /// 设备发现循环
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task DeviceDiscoveryLoop(CancellationToken cancellationToken)
    {
        _logger.LogDebug("设备发现循环已启动");

        try
        {
            // 启动时进行一次完整的设备发现
            await DiscoverDevicesAsync(cancellationToken);

            // 定期进行设备发现
            while (!cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(_communicationSettings.DeviceDiscoveryTimeout, cancellationToken);
                
                if (!cancellationToken.IsCancellationRequested)
                {
                    await DiscoverDevicesAsync(cancellationToken);
                }
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消，不记录错误
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设备发现循环异常");
        }

        _logger.LogDebug("设备发现循环已停止");
    }

    /// <summary>
    /// 处理通信错误
    /// </summary>
    /// <param name="deviceAddress">设备地址</param>
    /// <param name="errorMessage">错误信息</param>
    private async Task HandleCommunicationError(byte deviceAddress, string errorMessage)
    {
        var errorCount = _deviceErrorCounts.AddOrUpdate(deviceAddress, 1, (key, value) => value + 1);

        _logger.LogWarning("设备通信错误: 地址=0x{Address:X2}, 错误次数={Count}, 错误={Error}", 
                         deviceAddress, errorCount, errorMessage);

        // 错误次数超过阈值时标记设备离线
        if (errorCount >= _communicationSettings.RetryCount)
        {
            await _deviceManager.UpdateDeviceStatusAsync(deviceAddress, DeviceStatus.Error);
            _deviceErrorCounts.TryRemove(deviceAddress, out _);

            // 触发通信错误事件
            CommunicationError?.Invoke(this, new CommunicationErrorEventArgs
            {
                DeviceAddress = deviceAddress,
                ErrorMessage = errorMessage,
                ErrorType = CommunicationErrorType.NoResponse,
                Timestamp = DateTimeOffset.UtcNow
            });
        }
    }

    /// <summary>
    /// 从响应创建设备信息
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="parseResult">解析结果</param>
    /// <returns>设备信息</returns>
    private DeviceInfo CreateDeviceFromResponse(byte address, ParseResult parseResult)
    {
        var device = new DeviceInfo
        {
            Address = address,
            Status = DeviceStatus.Normal,
            LastCommunication = DateTimeOffset.UtcNow,
            PollingInterval = TimeSpan.FromMilliseconds(_communicationSettings.DefaultPollingInterval)
        };

        // 根据解析结果填充设备信息
        if (parseResult.CustomData != null)
        {
            if (parseResult.CustomData.TryGetValue("DeviceType", out var deviceType))
            {
                device.DeviceType = (DeviceType)(byte)deviceType;
            }

            if (parseResult.CustomData.TryGetValue("SerialNumber", out var serialNumber))
            {
                device.SerialNumber = serialNumber.ToString();
            }

            if (parseResult.CustomData.TryGetValue("HardwareVersion", out var hwVersion))
            {
                device.HardwareVersion = hwVersion.ToString();
            }

            if (parseResult.CustomData.TryGetValue("SoftwareVersion", out var swVersion))
            {
                device.FirmwareVersion = swVersion.ToString();
            }
        }

        return device;
    }

    /// <summary>
    /// 数据接收事件处理器
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="data">接收到的数据</param>
    private void OnDataReceived(object? sender, byte[] data)
    {
        try
        {
            var parseResult = _protocolManager.ParseFrame(data);
            if (parseResult.IsValid)
            {
                _logger.LogDebug("接收到有效数据: 源地址=0x{SourceAddress:X2}, 命令码=0x{CommandCode:X2}",
                               parseResult.SourceAddress, parseResult.CommandCode);

                // 触发设备数据接收事件
                DeviceDataReceived?.Invoke(this, new DeviceDataReceivedEventArgs
                {
                    DeviceAddress = parseResult.SourceAddress,
                    ParseResult = parseResult,
                    Timestamp = DateTimeOffset.UtcNow
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理接收数据失败");
        }
    }

    /// <summary>
    /// 串口错误事件处理器
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="exception">异常</param>
    private void OnSerialPortError(object? sender, Exception exception)
    {
        _logger.LogError(exception, "串口错误");
        
        CommunicationError?.Invoke(this, new CommunicationErrorEventArgs
        {
            DeviceAddress = 0x00, // 广播地址表示串口错误
            ErrorMessage = exception.Message,
            Exception = exception,
            ErrorType = CommunicationErrorType.ConnectionError,
            Timestamp = DateTimeOffset.UtcNow
        });
    }

    /// <summary>
    /// 设备状态变化事件处理器
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnDeviceStatusChanged(object? sender, DeviceStatusChangedEventArgs e)
    {
        DeviceStatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            StopAsync().Wait(10000);

            _communicationSemaphore?.Dispose();
            _cancellationTokenSource?.Dispose();

            // 取消事件订阅
            if (_serialPortManager != null)
            {
                _serialPortManager.DataReceived -= OnDataReceived;
                _serialPortManager.ErrorOccurred -= OnSerialPortError;
            }

            if (_deviceManager != null)
            {
                _deviceManager.DeviceStatusChanged -= OnDeviceStatusChanged;
            }

            _disposed = true;
        }
    }
}
