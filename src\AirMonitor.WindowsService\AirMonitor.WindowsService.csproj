<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-AirMonitor.WindowsService-074ab77f-4c09-4c40-8ec6-50cf00273b50</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="9.0.5" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="System.IO.Ports" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AirMonitor.Infrastructure\AirMonitor.Infrastructure.csproj" />
    <ProjectReference Include="..\AirMonitor.Protocols\AirMonitor.Protocols.csproj" />
  </ItemGroup>
</Project>
