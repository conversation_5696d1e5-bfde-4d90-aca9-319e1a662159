using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AirMonitor.Core.Entities.Monitoring;

namespace AirMonitor.Infrastructure.Data.Configurations;

/// <summary>
/// 监控数据实体配置
/// </summary>
public class MonitoringDataConfiguration : IEntityTypeConfiguration<MonitoringData>
{
    public void Configure(EntityTypeBuilder<MonitoringData> builder)
    {
        builder.ToTable("MonitoringData");
        
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.ParameterIndex)
            .IsRequired()
            .HasComment("参数索引");
            
        builder.Property(x => x.RawValue)
            .IsRequired()
            .HasComment("原始值");
            
        builder.Property(x => x.ParsedValue)
            .HasComment("解析后的值JSON");
            
        builder.Property(x => x.NumericValue)
            .HasPrecision(18, 6)
            .HasComment("数值型值");
            
        builder.Property(x => x.StringValue)
            .HasMaxLength(500)
            .HasComment("字符串值");
            
        builder.Property(x => x.Quality)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("数据质量");
        
        // 索引
        builder.HasIndex(x => new { x.DeviceId, x.ParameterIndex, x.Timestamp })
            .HasDatabaseName("IX_MonitoringData_Device_Parameter_Time");
            
        builder.HasIndex(x => x.Timestamp)
            .HasDatabaseName("IX_MonitoringData_Timestamp");
            
        builder.HasIndex(x => x.CreatedAt)
            .HasDatabaseName("IX_MonitoringData_CreatedAt");
            
        builder.HasIndex(x => x.Quality)
            .HasDatabaseName("IX_MonitoringData_Quality");
        
        // 关系配置
        builder.HasOne(x => x.Device)
            .WithMany(x => x.MonitoringData)
            .HasForeignKey(x => x.DeviceId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasOne(x => x.DeviceParameter)
            .WithMany(x => x.MonitoringData)
            .HasForeignKey(x => x.DeviceParameterId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
