{"version": 3, "targets": {"net8.0": {"AirMonitor.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "compile": {"bin/placeholder/AirMonitor.Core.dll": {}}, "runtime": {"bin/placeholder/AirMonitor.Core.dll": {}}}}}, "libraries": {"AirMonitor.Core/1.0.0": {"type": "project", "path": "../AirMonitor.Core/AirMonitor.Core.csproj", "msbuildProject": "../AirMonitor.Core/AirMonitor.Core.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["AirMonitor.Core >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages": {}, "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Protocols\\AirMonitor.Protocols.csproj", "projectName": "AirMonitor.Protocols", "projectPath": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Protocols\\AirMonitor.Protocols.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Protocols\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Project\\08 AirMonitor\\src\\AirMonitor.Core\\AirMonitor.Core.csproj": {"projectPath": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Core\\AirMonitor.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}