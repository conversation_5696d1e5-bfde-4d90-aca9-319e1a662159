namespace AirMonitor.Protocols.Constants;

/// <summary>
/// RS485通信协议常量定义
/// </summary>
public static class ProtocolConstants
{
    #region 帧结构常量
    
    /// <summary>
    /// 帧头标识
    /// </summary>
    public const byte FRAME_HEADER = 0x7E;
    
    /// <summary>
    /// 最小帧长度（头码+源地址+目标地址+命令码+长度+CRC16）
    /// </summary>
    public const int MIN_FRAME_LENGTH = 7;
    
    /// <summary>
    /// CRC16校验码长度
    /// </summary>
    public const int CRC_LENGTH = 2;
    
    /// <summary>
    /// 帧头长度
    /// </summary>
    public const int HEADER_LENGTH = 1;
    
    /// <summary>
    /// 地址字段长度
    /// </summary>
    public const int ADDRESS_LENGTH = 1;
    
    /// <summary>
    /// 命令码长度
    /// </summary>
    public const int COMMAND_LENGTH = 1;
    
    /// <summary>
    /// 数据长度字段长度
    /// </summary>
    public const int LENGTH_FIELD_LENGTH = 1;
    
    #endregion
    
    #region 命令码定义
    
    /// <summary>
    /// 参数读取命令
    /// </summary>
    public const byte CMD_PARAMETER_READ = 0x12;
    
    /// <summary>
    /// 参数写入命令
    /// </summary>
    public const byte CMD_PARAMETER_WRITE = 0x13;
    
    /// <summary>
    /// 系统状态数据命令
    /// </summary>
    public const byte CMD_SYSTEM_STATUS = 0xA1;
    
    /// <summary>
    /// 设备信息查询命令
    /// </summary>
    public const byte CMD_DEVICE_INFO = 0xA2;
    
    /// <summary>
    /// 心跳包命令
    /// </summary>
    public const byte CMD_HEARTBEAT = 0xFF;
    
    /// <summary>
    /// 设备发现命令
    /// </summary>
    public const byte CMD_DEVICE_DISCOVERY = 0xFE;
    
    /// <summary>
    /// 控制命令基础码
    /// </summary>
    public const byte CMD_CONTROL_BASE = 0x20;
    
    #endregion
    
    #region 地址定义
    
    /// <summary>
    /// 广播地址
    /// </summary>
    public const byte BROADCAST_ADDRESS = 0x00;
    
    /// <summary>
    /// 主设备地址（通常为外机F1）
    /// </summary>
    public const byte MASTER_ADDRESS = 0xF1;
    
    /// <summary>
    /// 最小设备地址
    /// </summary>
    public const byte MIN_DEVICE_ADDRESS = 0x01;
    
    /// <summary>
    /// 最大设备地址
    /// </summary>
    public const byte MAX_DEVICE_ADDRESS = 0xFE;
    
    #endregion
    
    #region 协议格式标识
    
    /// <summary>
    /// 参数索引格式标识
    /// </summary>
    public const byte FORMAT_PARAMETER_INDEX = 0x01;
    
    /// <summary>
    /// 自定义数据格式标识
    /// </summary>
    public const byte FORMAT_CUSTOM_DATA = 0x02;
    
    #endregion
    
    #region 数据限制
    
    /// <summary>
    /// 最大数据长度
    /// </summary>
    public const int MAX_DATA_LENGTH = 255;
    
    /// <summary>
    /// 最大帧长度
    /// </summary>
    public const int MAX_FRAME_LENGTH = MIN_FRAME_LENGTH + MAX_DATA_LENGTH;
    
    /// <summary>
    /// 参数索引最大值
    /// </summary>
    public const byte MAX_PARAMETER_INDEX = 0xFF;
    
    /// <summary>
    /// 单次读取最大参数个数
    /// </summary>
    public const byte MAX_PARAMETER_COUNT = 0x20;
    
    #endregion
    
    #region 超时设置
    
    /// <summary>
    /// 默认通信超时时间（毫秒）
    /// </summary>
    public const int DEFAULT_TIMEOUT_MS = 5000;
    
    /// <summary>
    /// 心跳超时时间（毫秒）
    /// </summary>
    public const int HEARTBEAT_TIMEOUT_MS = 2000;
    
    /// <summary>
    /// 设备发现超时时间（毫秒）
    /// </summary>
    public const int DISCOVERY_TIMEOUT_MS = 10000;
    
    /// <summary>
    /// 重试间隔时间（毫秒）
    /// </summary>
    public const int RETRY_INTERVAL_MS = 1000;
    
    #endregion
    
    #region CRC16多项式
    
    /// <summary>
    /// Modbus CRC16多项式
    /// </summary>
    public const ushort CRC16_POLYNOMIAL = 0xA001;
    
    /// <summary>
    /// CRC16初始值
    /// </summary>
    public const ushort CRC16_INITIAL_VALUE = 0xFFFF;
    
    #endregion
    
    #region 错误码定义
    
    /// <summary>
    /// 成功
    /// </summary>
    public const byte ERROR_SUCCESS = 0x00;
    
    /// <summary>
    /// 无效命令
    /// </summary>
    public const byte ERROR_INVALID_COMMAND = 0x01;
    
    /// <summary>
    /// 无效参数
    /// </summary>
    public const byte ERROR_INVALID_PARAMETER = 0x02;
    
    /// <summary>
    /// 设备忙
    /// </summary>
    public const byte ERROR_DEVICE_BUSY = 0x03;
    
    /// <summary>
    /// 通信超时
    /// </summary>
    public const byte ERROR_TIMEOUT = 0x04;
    
    /// <summary>
    /// CRC校验失败
    /// </summary>
    public const byte ERROR_CRC_FAILED = 0x05;
    
    #endregion
}
