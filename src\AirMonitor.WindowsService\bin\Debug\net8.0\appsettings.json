{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information", "AirMonitor": "Debug"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "AirMonitor": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/airmonitor-service-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}]}, "SerialPort": {"DefaultPortName": "COM1", "BaudRate": 9600, "DataBits": 8, "StopBits": "One", "Parity": "None", "ReadTimeout": 5000, "WriteTimeout": 5000, "ReceiveBufferSize": 4096, "SendBufferSize": 4096, "DtrEnable": false, "RtsEnable": false, "RetryCount": 3, "RetryInterval": 1000, "AutoReconnect": true, "HeartbeatInterval": 30000}, "Communication": {"DefaultPollingInterval": 5000, "DeviceDiscoveryTimeout": 60000, "MaxConcurrentOperations": 10, "DataCacheSize": 1000, "BatchProcessingSize": 100, "EnableDataValidation": true, "EnablePerformanceLogging": false, "DeviceOfflineTimeout": 120000, "RetryCount": 3, "RetryInterval": 1000, "EnableAutoReconnect": true, "HeartbeatInterval": 30000}}