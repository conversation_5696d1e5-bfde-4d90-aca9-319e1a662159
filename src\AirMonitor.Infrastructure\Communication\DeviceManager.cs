using System.Collections.Concurrent;
using AirMonitor.Core.Enums;
using AirMonitor.Infrastructure.Configuration;
using AirMonitor.Protocols.Interfaces;
using AirMonitor.Protocols.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace AirMonitor.Infrastructure.Communication;

/// <summary>
/// 设备管理器实现
/// </summary>
public class DeviceManager : IDeviceManager, IDisposable
{
    private readonly ILogger<DeviceManager> _logger;
    private readonly CommunicationSettings _settings;
    private readonly ISerialCommunicationService _communicationService;
    private readonly ConcurrentDictionary<byte, DeviceInfo> _devices;
    private readonly ConcurrentDictionary<byte, DateTime> _lastPollingTimes;
    private readonly SemaphoreSlim _operationSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;
    
    private Task? _pollingTask;
    private bool _disposed;

    /// <summary>
    /// 设备状态变化事件
    /// </summary>
    public event EventHandler<DeviceStatusChangedEventArgs>? DeviceStatusChanged;

    /// <summary>
    /// 设备发现事件
    /// </summary>
    public event EventHandler<DeviceInfo>? DeviceDiscovered;

    /// <summary>
    /// 设备离线事件
    /// </summary>
    public event EventHandler<DeviceInfo>? DeviceOffline;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="settings">通信设置</param>
    /// <param name="communicationService">通信服务</param>
    public DeviceManager(
        ILogger<DeviceManager> logger,
        IOptions<CommunicationSettings> settings,
        ISerialCommunicationService communicationService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _settings = settings?.Value ?? throw new ArgumentNullException(nameof(settings));
        _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));

        _devices = new ConcurrentDictionary<byte, DeviceInfo>();
        _lastPollingTimes = new ConcurrentDictionary<byte, DateTime>();
        _operationSemaphore = new SemaphoreSlim(1, 1);
        _cancellationTokenSource = new CancellationTokenSource();
    }

    /// <summary>
    /// 注册设备
    /// </summary>
    /// <param name="device">设备信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task RegisterDeviceAsync(DeviceInfo device, CancellationToken cancellationToken = default)
    {
        if (device == null)
            throw new ArgumentNullException(nameof(device));

        await _operationSemaphore.WaitAsync(cancellationToken);
        try
        {
            var isNewDevice = !_devices.ContainsKey(device.Address);
            
            _devices.AddOrUpdate(device.Address, device, (key, oldValue) =>
            {
                // 保留一些现有信息
                device.LastCommunication = oldValue.LastCommunication;
                if (device.PollingInterval == TimeSpan.Zero)
                    device.PollingInterval = oldValue.PollingInterval;
                return device;
            });

            _lastPollingTimes.TryAdd(device.Address, DateTime.UtcNow);

            if (isNewDevice)
            {
                _logger.LogInformation("设备已注册: 地址=0x{Address:X2}, 类型={DeviceType}, 序列号={SerialNumber}",
                                     device.Address, device.DeviceType, device.SerialNumber);
                DeviceDiscovered?.Invoke(this, device);
            }
            else
            {
                _logger.LogDebug("设备信息已更新: 地址=0x{Address:X2}", device.Address);
            }
        }
        finally
        {
            _operationSemaphore.Release();
        }
    }

    /// <summary>
    /// 注销设备
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task UnregisterDeviceAsync(byte address, CancellationToken cancellationToken = default)
    {
        await _operationSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_devices.TryRemove(address, out var device))
            {
                _lastPollingTimes.TryRemove(address, out _);
                
                _logger.LogInformation("设备已注销: 地址=0x{Address:X2}, 类型={DeviceType}",
                                     address, device.DeviceType);
                DeviceOffline?.Invoke(this, device);
            }
        }
        finally
        {
            _operationSemaphore.Release();
        }
    }

    /// <summary>
    /// 获取设备信息
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设备信息</returns>
    public async Task<DeviceInfo?> GetDeviceAsync(byte address, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // 保持异步接口一致性
        _devices.TryGetValue(address, out var device);
        return device;
    }

    /// <summary>
    /// 获取所有设备
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设备列表</returns>
    public async Task<IEnumerable<DeviceInfo>> GetAllDevicesAsync(CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // 保持异步接口一致性
        return _devices.Values.ToList();
    }

    /// <summary>
    /// 更新设备状态
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="status">新状态</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task UpdateDeviceStatusAsync(byte address, DeviceStatus status, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // 保持异步接口一致性
        
        if (_devices.TryGetValue(address, out var device))
        {
            var oldStatus = device.Status;
            device.Status = status;
            device.LastCommunication = DateTimeOffset.UtcNow;

            if (oldStatus != status)
            {
                _logger.LogInformation("设备状态变化: 地址=0x{Address:X2}, {OldStatus} -> {NewStatus}",
                                     address, oldStatus, status);

                DeviceStatusChanged?.Invoke(this, new DeviceStatusChangedEventArgs
                {
                    DeviceAddress = address,
                    OldStatus = oldStatus,
                    NewStatus = status,
                    Timestamp = DateTimeOffset.UtcNow
                });
            }
        }
    }

    /// <summary>
    /// 获取设备状态
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设备状态</returns>
    public async Task<DeviceStatus> GetDeviceStatusAsync(byte address, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // 保持异步接口一致性
        
        if (_devices.TryGetValue(address, out var device))
        {
            return device.Status;
        }
        
        return DeviceStatus.Unknown;
    }

    /// <summary>
    /// 检查设备是否在线
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否在线</returns>
    public async Task<bool> IsDeviceOnlineAsync(byte address, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // 保持异步接口一致性
        
        if (_devices.TryGetValue(address, out var device))
        {
            var timeSinceLastCommunication = DateTimeOffset.UtcNow - device.LastCommunication;
            return device.Status == DeviceStatus.Normal && 
                   timeSinceLastCommunication.TotalMilliseconds < _settings.DeviceOfflineTimeout;
        }
        
        return false;
    }

    /// <summary>
    /// 启动设备轮询
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task StartPollingAsync(CancellationToken cancellationToken = default)
    {
        if (_pollingTask != null && !_pollingTask.IsCompleted)
        {
            _logger.LogWarning("设备轮询已在运行中");
            return;
        }

        _logger.LogInformation("启动设备轮询服务");
        
        var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
            cancellationToken, _cancellationTokenSource.Token);

        _pollingTask = Task.Run(() => PollingLoop(combinedCts.Token), combinedCts.Token);
        
        await Task.CompletedTask;
    }

    /// <summary>
    /// 停止设备轮询
    /// </summary>
    public async Task StopPollingAsync()
    {
        _logger.LogInformation("停止设备轮询服务");
        
        _cancellationTokenSource.Cancel();
        
        if (_pollingTask != null)
        {
            try
            {
                await _pollingTask;
            }
            catch (OperationCanceledException)
            {
                // 正常取消，不记录错误
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止设备轮询时发生异常");
            }
            finally
            {
                _pollingTask = null;
            }
        }
    }

    /// <summary>
    /// 获取轮询间隔
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>轮询间隔</returns>
    public async Task<TimeSpan> GetPollingIntervalAsync(byte address, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // 保持异步接口一致性
        
        if (_devices.TryGetValue(address, out var device))
        {
            return device.PollingInterval;
        }
        
        return TimeSpan.FromMilliseconds(_settings.DefaultPollingInterval);
    }

    /// <summary>
    /// 设置轮询间隔
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="interval">轮询间隔</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task SetPollingIntervalAsync(byte address, TimeSpan interval, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // 保持异步接口一致性
        
        if (_devices.TryGetValue(address, out var device))
        {
            device.PollingInterval = interval;
            _logger.LogDebug("设备轮询间隔已更新: 地址=0x{Address:X2}, 间隔={Interval}ms",
                           address, interval.TotalMilliseconds);
        }
    }

    /// <summary>
    /// 轮询循环
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task PollingLoop(CancellationToken cancellationToken)
    {
        _logger.LogDebug("设备轮询循环已启动");

        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                var devices = _devices.Values.ToList();
                var pollingTasks = new List<Task>();

                foreach (var device in devices)
                {
                    if (ShouldPollDevice(device))
                    {
                        var task = PollDeviceAsync(device, cancellationToken);
                        pollingTasks.Add(task);
                    }
                }

                // 等待所有轮询任务完成
                if (pollingTasks.Count > 0)
                {
                    await Task.WhenAll(pollingTasks);
                }

                // 等待下一轮轮询
                await Task.Delay(_settings.DefaultPollingInterval, cancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消，不记录错误
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设备轮询循环异常");
        }

        _logger.LogDebug("设备轮询循环已停止");
    }

    /// <summary>
    /// 判断是否应该轮询设备
    /// </summary>
    /// <param name="device">设备信息</param>
    /// <returns>是否应该轮询</returns>
    private bool ShouldPollDevice(DeviceInfo device)
    {
        if (!_lastPollingTimes.TryGetValue(device.Address, out var lastPollingTime))
        {
            return true; // 从未轮询过
        }

        var timeSinceLastPolling = DateTime.UtcNow - lastPollingTime;
        return timeSinceLastPolling >= device.PollingInterval;
    }

    /// <summary>
    /// 轮询单个设备
    /// </summary>
    /// <param name="device">设备信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task PollDeviceAsync(DeviceInfo device, CancellationToken cancellationToken)
    {
        try
        {
            _lastPollingTimes[device.Address] = DateTime.UtcNow;

            // 发送心跳包检查设备状态
            var heartbeatData = new byte[] { 0x00 };
            var response = await _communicationService.SendCommandAsync(
                device.Address, 0xFF, heartbeatData, 
                TimeSpan.FromSeconds(5), cancellationToken);

            if (response != null)
            {
                await UpdateDeviceStatusAsync(device.Address, DeviceStatus.Normal, cancellationToken);
                device.LastCommunication = DateTimeOffset.UtcNow;
                
                _logger.LogDebug("设备轮询成功: 地址=0x{Address:X2}", device.Address);
            }
            else
            {
                await UpdateDeviceStatusAsync(device.Address, DeviceStatus.Error, cancellationToken);
                _logger.LogWarning("设备轮询失败: 地址=0x{Address:X2}", device.Address);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "轮询设备失败: 地址=0x{Address:X2}", device.Address);
            await UpdateDeviceStatusAsync(device.Address, DeviceStatus.Error, cancellationToken);
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            StopPollingAsync().Wait(5000);
            
            _operationSemaphore?.Dispose();
            _cancellationTokenSource?.Dispose();
            
            _disposed = true;
        }
    }
}
