using AirMonitor.Core.Enums;
using AirMonitor.Protocols.Models;

namespace AirMonitor.Protocols.Interfaces;

/// <summary>
/// 协议解析器接口
/// </summary>
public interface IProtocolParser
{
    /// <summary>
    /// 支持的协议格式
    /// </summary>
    ProtocolFormat SupportedFormat { get; }
    
    /// <summary>
    /// 解析数据帧
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <returns>解析结果</returns>
    ParseResult ParseFrame(byte[] rawData);
    
    /// <summary>
    /// 验证数据帧格式
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <returns>是否为有效格式</returns>
    bool CanParse(byte[] rawData);
}

/// <summary>
/// 协议管理器接口
/// </summary>
public interface IProtocolManager
{
    /// <summary>
    /// 构建数据帧
    /// </summary>
    /// <param name="sourceAddress">源地址</param>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="commandCode">命令码</param>
    /// <param name="data">数据</param>
    /// <returns>完整数据帧</returns>
    byte[] BuildFrame(byte sourceAddress, byte targetAddress, byte commandCode, byte[] data);
    
    /// <summary>
    /// 构建参数读取帧
    /// </summary>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="parameterIndex">参数索引</param>
    /// <param name="count">参数个数</param>
    /// <returns>参数读取帧</returns>
    byte[] BuildParameterReadFrame(byte targetAddress, byte parameterIndex, byte count);
    
    /// <summary>
    /// 构建控制命令帧
    /// </summary>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="controlData">控制数据</param>
    /// <returns>控制命令帧</returns>
    byte[] BuildControlFrame(byte targetAddress, byte[] controlData);
    
    /// <summary>
    /// 构建心跳帧
    /// </summary>
    /// <param name="targetAddress">目标地址</param>
    /// <returns>心跳帧</returns>
    byte[] BuildHeartbeatFrame(byte targetAddress);
    
    /// <summary>
    /// 解析数据帧
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <returns>解析结果</returns>
    ParseResult ParseFrame(byte[] rawData);
    
    /// <summary>
    /// 验证数据帧
    /// </summary>
    /// <param name="frame">数据帧</param>
    /// <returns>是否有效</returns>
    bool ValidateFrame(byte[] frame);
    
    /// <summary>
    /// 计算CRC16校验码
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="start">起始位置</param>
    /// <param name="length">长度</param>
    /// <returns>CRC16值</returns>
    ushort CalculateCRC16(byte[] data, int start, int length);
    
    /// <summary>
    /// 检测协议格式
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <returns>协议格式</returns>
    ProtocolFormat DetectFormat(byte[] rawData);
    
    /// <summary>
    /// 获取协议解析器
    /// </summary>
    /// <param name="format">协议格式</param>
    /// <returns>协议解析器</returns>
    IProtocolParser GetParser(ProtocolFormat format);
    
    /// <summary>
    /// 注册协议解析器
    /// </summary>
    /// <param name="parser">协议解析器</param>
    void RegisterParser(IProtocolParser parser);
}

/// <summary>
/// 串口管理器接口
/// </summary>
public interface ISerialPortManager
{
    /// <summary>
    /// 是否已连接
    /// </summary>
    bool IsConnected { get; }
    
    /// <summary>
    /// 当前端口名称
    /// </summary>
    string? CurrentPortName { get; }
    
    /// <summary>
    /// 连接串口
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接是否成功</returns>
    Task<bool> ConnectAsync(string portName, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 断开连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>断开是否成功</returns>
    Task<bool> DisconnectAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送数据
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送是否成功</returns>
    Task<bool> SendAsync(byte[] data, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 接收数据
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收到的数据</returns>
    Task<byte[]?> ReceiveAsync(TimeSpan timeout, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 清空接收缓冲区
    /// </summary>
    void ClearReceiveBuffer();
    
    /// <summary>
    /// 清空发送缓冲区
    /// </summary>
    void ClearSendBuffer();
    
    /// <summary>
    /// 获取可用串口列表
    /// </summary>
    /// <returns>串口名称列表</returns>
    string[] GetAvailablePorts();
    
    /// <summary>
    /// 数据接收事件
    /// </summary>
    event EventHandler<byte[]>? DataReceived;
    
    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    event EventHandler<bool>? ConnectionStateChanged;
    
    /// <summary>
    /// 错误事件
    /// </summary>
    event EventHandler<Exception>? ErrorOccurred;
}

/// <summary>
/// 设备管理器接口
/// </summary>
public interface IDeviceManager
{
    /// <summary>
    /// 注册设备
    /// </summary>
    /// <param name="device">设备信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task RegisterDeviceAsync(DeviceInfo device, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 注销设备
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task UnregisterDeviceAsync(byte address, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取设备信息
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设备信息</returns>
    Task<DeviceInfo?> GetDeviceAsync(byte address, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取所有设备
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设备列表</returns>
    Task<IEnumerable<DeviceInfo>> GetAllDevicesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新设备状态
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="status">新状态</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task UpdateDeviceStatusAsync(byte address, DeviceStatus status, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取设备状态
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设备状态</returns>
    Task<DeviceStatus> GetDeviceStatusAsync(byte address, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查设备是否在线
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否在线</returns>
    Task<bool> IsDeviceOnlineAsync(byte address, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 启动设备轮询
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task StartPollingAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 停止设备轮询
    /// </summary>
    Task StopPollingAsync();
    
    /// <summary>
    /// 获取轮询间隔
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>轮询间隔</returns>
    Task<TimeSpan> GetPollingIntervalAsync(byte address, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 设置轮询间隔
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="interval">轮询间隔</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task SetPollingIntervalAsync(byte address, TimeSpan interval, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 设备状态变化事件
    /// </summary>
    event EventHandler<DeviceStatusChangedEventArgs>? DeviceStatusChanged;
    
    /// <summary>
    /// 设备发现事件
    /// </summary>
    event EventHandler<DeviceInfo>? DeviceDiscovered;
    
    /// <summary>
    /// 设备离线事件
    /// </summary>
    event EventHandler<DeviceInfo>? DeviceOffline;
}

/// <summary>
/// 串口通信服务接口
/// </summary>
public interface ISerialCommunicationService
{
    /// <summary>
    /// 启动服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动是否成功</returns>
    Task<bool> StartAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task StopAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检查连接状态
    /// </summary>
    /// <returns>是否已连接</returns>
    Task<bool> IsConnectedAsync();
    
    /// <summary>
    /// 发送命令
    /// </summary>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="commandCode">命令码</param>
    /// <param name="data">数据</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应数据</returns>
    Task<byte[]?> SendCommandAsync(byte targetAddress, byte commandCode, byte[] data, 
        TimeSpan? timeout = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送命令并解析响应
    /// </summary>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="commandCode">命令码</param>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解析结果</returns>
    Task<ParseResult?> SendAndParseAsync(byte targetAddress, byte commandCode, byte[] data,
        CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发现设备
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发现的设备列表</returns>
    Task<IEnumerable<DeviceInfo>> DiscoverDevicesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取设备信息
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设备信息</returns>
    Task<DeviceInfo?> GetDeviceInfoAsync(byte address, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 设备数据接收事件
    /// </summary>
    event EventHandler<DeviceDataReceivedEventArgs>? DeviceDataReceived;
    
    /// <summary>
    /// 设备状态变化事件
    /// </summary>
    event EventHandler<DeviceStatusChangedEventArgs>? DeviceStatusChanged;
    
    /// <summary>
    /// 通信错误事件
    /// </summary>
    event EventHandler<CommunicationErrorEventArgs>? CommunicationError;
}
