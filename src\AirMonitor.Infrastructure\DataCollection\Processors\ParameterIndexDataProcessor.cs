using System.Diagnostics;
using AirMonitor.Core.Entities.Devices;
using AirMonitor.Core.Entities.Monitoring;
using AirMonitor.Core.Entities.Parameters;
using AirMonitor.Core.Enums;
using AirMonitor.Core.Interfaces;
using AirMonitor.Infrastructure.DataCollection.Interfaces;
using AirMonitor.Infrastructure.DataCollection.Models;
using AirMonitor.Protocols.Models;
using Microsoft.Extensions.Logging;

namespace AirMonitor.Infrastructure.DataCollection.Processors;

/// <summary>
/// 参数索引数据处理器
/// </summary>
public class ParameterIndexDataProcessor : IDataProcessor
{
    private readonly ILogger<ParameterIndexDataProcessor> _logger;
    private readonly IDeviceRepository _deviceRepository;
    private readonly IParameterDefinitionRepository _parameterRepository;
    private readonly IMonitoringDataRepository _monitoringDataRepository;
    private readonly IDataTransformer _dataTransformer;
    private readonly ProcessorStatistics _statistics;
    private readonly object _statisticsLock = new();

    public string Name => nameof(ParameterIndexDataProcessor);
    public int Priority => 100;

    public ParameterIndexDataProcessor(
        ILogger<ParameterIndexDataProcessor> logger,
        IDeviceRepository deviceRepository,
        IParameterDefinitionRepository parameterRepository,
        IMonitoringDataRepository monitoringDataRepository,
        IDataTransformer dataTransformer)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _deviceRepository = deviceRepository ?? throw new ArgumentNullException(nameof(deviceRepository));
        _parameterRepository = parameterRepository ?? throw new ArgumentNullException(nameof(parameterRepository));
        _monitoringDataRepository = monitoringDataRepository ?? throw new ArgumentNullException(nameof(monitoringDataRepository));
        _dataTransformer = dataTransformer ?? throw new ArgumentNullException(nameof(dataTransformer));

        _statistics = new ProcessorStatistics
        {
            ProcessorName = Name,
            IsEnabled = true
        };
    }

    /// <summary>
    /// 是否可以处理指定数据
    /// </summary>
    /// <param name="parseResult">解析结果</param>
    /// <returns>是否可以处理</returns>
    public bool CanProcess(object parseResult)
    {
        return parseResult is ParseResult result &&
               result.Format == ProtocolFormat.ParameterIndex &&
               result.Parameters != null &&
               result.Parameters.Length > 0;
    }

    /// <summary>
    /// 处理单条数据
    /// </summary>
    /// <param name="parseResult">解析结果</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理后的数据</returns>
    public async Task<ProcessedData> ProcessAsync(object parseResult, CancellationToken cancellationToken = default)
    {
        if (!CanProcess(parseResult))
            throw new InvalidOperationException("不支持的数据格式");

        var stopwatch = Stopwatch.StartNew();
        var result = (ParseResult)parseResult;

        try
        {
            // 获取设备信息
            var device = await _deviceRepository.GetByIdAsync(result.SourceAddress);
            if (device == null)
            {
                _logger.LogWarning("未找到设备: 地址=0x{Address:X2}", result.SourceAddress);
                throw new InvalidOperationException($"未找到设备: 地址=0x{result.SourceAddress:X2}");
            }

            var processedDataList = new List<ProcessedData>();

            // 处理每个参数
            foreach (var parameter in result.Parameters!)
            {
                try
                {
                    var processedData = await ProcessParameterAsync(device, parameter, result.Timestamp);
                    if (processedData != null)
                    {
                        processedDataList.Add(processedData);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理参数失败: 设备={DeviceId}, 参数索引=0x{Index:X2}", 
                                   device.Id, parameter.Index);
                    
                    lock (_statisticsLock)
                    {
                        _statistics.ErrorCount++;
                    }
                }
            }

            // 批量存储监控数据
            if (processedDataList.Count > 0)
            {
                await StoreBatchMonitoringDataAsync(processedDataList, device, cancellationToken);
            }

            stopwatch.Stop();
            UpdateStatistics(stopwatch.Elapsed, true);

            return processedDataList.FirstOrDefault() ?? new ProcessedData
            {
                DeviceAddress = result.SourceAddress,
                Timestamp = result.Timestamp,
                ProcessorName = Name,
                Quality = DataQuality.Bad
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            UpdateStatistics(stopwatch.Elapsed, false);
            _logger.LogError(ex, "处理数据失败: 设备地址=0x{Address:X2}", result.SourceAddress);
            throw;
        }
    }

    /// <summary>
    /// 批量处理数据
    /// </summary>
    /// <param name="parseResults">解析结果集合</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理后的数据集合</returns>
    public async Task<IEnumerable<ProcessedData>> ProcessBatchAsync(IEnumerable<object> parseResults, 
        CancellationToken cancellationToken = default)
    {
        var results = new List<ProcessedData>();
        var tasks = parseResults.Select(async parseResult =>
        {
            try
            {
                return await ProcessAsync(parseResult, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量处理中单条数据处理失败");
                return null;
            }
        });

        var processedResults = await Task.WhenAll(tasks);
        results.AddRange(processedResults.Where(r => r != null)!);

        return results;
    }

    /// <summary>
    /// 获取处理器统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public async Task<ProcessorStatistics> GetStatisticsAsync()
    {
        await Task.CompletedTask;
        
        lock (_statisticsLock)
        {
            return new ProcessorStatistics
            {
                ProcessorName = _statistics.ProcessorName,
                ProcessedCount = _statistics.ProcessedCount,
                ErrorCount = _statistics.ErrorCount,
                AverageProcessingTime = _statistics.AverageProcessingTime,
                LastProcessedTime = _statistics.LastProcessedTime,
                IsEnabled = _statistics.IsEnabled
            };
        }
    }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    public async Task ResetStatisticsAsync()
    {
        await Task.CompletedTask;
        
        lock (_statisticsLock)
        {
            _statistics.ProcessedCount = 0;
            _statistics.ErrorCount = 0;
            _statistics.AverageProcessingTime = TimeSpan.Zero;
            _statistics.LastProcessedTime = DateTimeOffset.UtcNow;
        }
        
        _logger.LogInformation("处理器 {ProcessorName} 统计信息已重置", Name);
    }

    /// <summary>
    /// 处理单个参数
    /// </summary>
    /// <param name="device">设备信息</param>
    /// <param name="parameter">参数数据</param>
    /// <param name="timestamp">时间戳</param>
    /// <returns>处理后的数据</returns>
    private async Task<ProcessedData?> ProcessParameterAsync(Device device, ParameterData parameter, DateTimeOffset timestamp)
    {
        // 获取参数定义
        var parameterDef = await GetParameterDefinitionAsync(device, parameter.Index);
        if (parameterDef == null)
        {
            _logger.LogDebug("未找到参数定义: 设备={DeviceId}, 参数索引=0x{Index:X2}", 
                           device.Id, parameter.Index);
            return null;
        }

        // 创建处理后的数据
        var processedData = new ProcessedData
        {
            DeviceAddress = (byte)device.Id, // 临时使用设备ID作为地址
            ParameterIndex = parameter.Index,
            RawValue = parameter.RawValue,
            ParsedValue = parameter.ParsedValue,
            ValueFormat = parameter.Format,
            Quality = parameter.Quality,
            Timestamp = timestamp,
            ProcessorName = Name
        };

        // 添加元数据
        processedData.Metadata["DeviceId"] = device.Id;
        processedData.Metadata["ParameterDefinitionId"] = parameterDef.Id;
        processedData.Metadata["ParameterName"] = parameterDef.Name;
        processedData.Metadata["Unit"] = parameterDef.Unit ?? "";
        processedData.Metadata["ParameterIndex"] = parameter.Index;

        return processedData;
    }

    /// <summary>
    /// 获取参数定义
    /// </summary>
    /// <param name="device">设备信息</param>
    /// <param name="parameterIndex">参数索引</param>
    /// <returns>参数定义</returns>
    private async Task<ParameterDefinition?> GetParameterDefinitionAsync(Device device, byte parameterIndex)
    {
        try
        {
            // 从数据库获取参数定义
            return await _parameterRepository.GetByIdAsync(parameterIndex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取参数定义失败: 设备={DeviceId}, 参数索引=0x{Index:X2}",
                           device.Id, parameterIndex);
            return null;
        }
    }

    /// <summary>
    /// 批量存储监控数据
    /// </summary>
    /// <param name="processedDataList">处理后的数据列表</param>
    /// <param name="device">设备信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task StoreBatchMonitoringDataAsync(List<ProcessedData> processedDataList, 
        Device device, CancellationToken cancellationToken)
    {
        try
        {
            var monitoringDataList = new List<MonitoringData>();

            foreach (var processedData in processedDataList)
            {
                var parameterDefId = (int)processedData.Metadata["ParameterDefinitionId"];

                // 转换为监控数据实体
                var paramDef = new ParameterDefinition { Id = parameterDefId };
                var monitoringData = await _dataTransformer.TransformAsync(
                    processedData, device, paramDef);

                if (monitoringData is MonitoringData data)
                {
                    monitoringDataList.Add(data);
                }
            }

            // 批量保存到数据库
            if (monitoringDataList.Count > 0)
            {
                await _monitoringDataRepository.SaveBatchAsync(monitoringDataList);
                
                _logger.LogDebug("批量存储监控数据完成: 设备={DeviceId}, 数据条数={Count}", 
                               device.Id, monitoringDataList.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量存储监控数据失败: 设备={DeviceId}", device.Id);
            throw;
        }
    }



    /// <summary>
    /// 更新统计信息
    /// </summary>
    /// <param name="processingTime">处理时间</param>
    /// <param name="success">是否成功</param>
    private void UpdateStatistics(TimeSpan processingTime, bool success)
    {
        lock (_statisticsLock)
        {
            _statistics.ProcessedCount++;
            _statistics.LastProcessedTime = DateTimeOffset.UtcNow;

            if (!success)
            {
                _statistics.ErrorCount++;
            }

            // 更新平均处理时间
            var totalTime = _statistics.AverageProcessingTime.TotalMilliseconds * (_statistics.ProcessedCount - 1) + 
                           processingTime.TotalMilliseconds;
            _statistics.AverageProcessingTime = TimeSpan.FromMilliseconds(totalTime / _statistics.ProcessedCount);
        }
    }
}
