using AirMonitor.Core.Enums;
using AirMonitor.Protocols.Constants;
using AirMonitor.Protocols.Parsers;
using AirMonitor.Protocols.Services;

namespace AirMonitor.UnitTests;

/// <summary>
/// 协议管理器测试
/// </summary>
public class ProtocolManagerTests
{
    [Fact]
    public void BuildFrame_ValidInput_ReturnsCorrectFrame()
    {
        // Arrange
        var protocolManager = new ProtocolManager();
        var data = new byte[] { 0x35, 0x0E, 0x01, 0x4F };

        // Act
        var frame = protocolManager.BuildFrame(0xF1, 0xF1, 0x12, data);

        // Assert
        Assert.Equal(ProtocolConstants.FRAME_HEADER, frame[0]); // 头码
        Assert.Equal(0xF1, frame[1]); // 源地址
        Assert.Equal(0xF1, frame[2]); // 目标地址
        Assert.Equal(0x12, frame[3]); // 命令码
        Assert.True(protocolManager.ValidateFrame(frame)); // CRC校验
    }

    [Fact]
    public void BuildParameterReadFrame_ValidInput_ReturnsCorrectFrame()
    {
        // Arrange
        var protocolManager = new ProtocolManager();

        // Act
        var frame = protocolManager.BuildParameterReadFrame(0xF1, 0x35, 0x0E);

        // Assert
        Assert.Equal(ProtocolConstants.FRAME_HEADER, frame[0]);
        Assert.Equal(ProtocolConstants.MASTER_ADDRESS, frame[1]);
        Assert.Equal(0xF1, frame[2]);
        Assert.Equal(ProtocolConstants.CMD_PARAMETER_READ, frame[3]);
        Assert.Equal(0x35, frame[5]); // 参数索引
        Assert.Equal(0x0E, frame[6]); // 参数个数
    }

    [Fact]
    public void DetectFormat_ParameterReadCommand_ReturnsParameterIndex()
    {
        // Arrange
        var protocolManager = new ProtocolManager();
        var rawData = new byte[] { 0x7E, 0xF1, 0xF1, 0x12, 0x25 };

        // Act
        var format = protocolManager.DetectFormat(rawData);

        // Assert
        Assert.Equal(ProtocolFormat.ParameterIndex, format);
    }

    [Fact]
    public void DetectFormat_SystemStatusCommand_ReturnsCustomData()
    {
        // Arrange
        var protocolManager = new ProtocolManager();
        var rawData = new byte[] { 0x7E, 0xF1, 0x00, 0xA1, 0x17, 0x23, 0x60 };

        // Act
        var format = protocolManager.DetectFormat(rawData);

        // Assert
        Assert.Equal(ProtocolFormat.CustomData, format);
    }

    [Fact]
    public void CalculateCRC16_ValidData_ReturnsCorrectCRC()
    {
        // Arrange
        var protocolManager = new ProtocolManager();
        var data = new byte[] { 0xF1, 0xF1, 0x12, 0x05, 0x00 };

        // Act
        var crc = protocolManager.CalculateCRC16(data, 0, data.Length);

        // Assert
        Assert.True(crc > 0); // CRC应该不为0
    }
}

/// <summary>
/// 参数索引解析器测试
/// </summary>
public class ParameterIndexParserTests
{
    [Fact]
    public void CanParse_ParameterReadFrame_ReturnsTrue()
    {
        // Arrange
        var parser = new ParameterIndexParser();
        var rawData = new byte[] { 0x7E, 0xF1, 0xF1, 0x12, 0x25, 0x35, 0x0E };

        // Act
        var canParse = parser.CanParse(rawData);

        // Assert
        Assert.True(canParse);
    }

    [Fact]
    public void CanParse_SystemStatusFrame_ReturnsFalse()
    {
        // Arrange
        var parser = new ParameterIndexParser();
        var rawData = new byte[] { 0x7E, 0xF1, 0x00, 0xA1, 0x17, 0x23, 0x60 };

        // Act
        var canParse = parser.CanParse(rawData);

        // Assert
        Assert.False(canParse);
    }

    [Fact]
    public void ParseFrame_InvalidFrameHeader_ReturnsInvalidResult()
    {
        // Arrange
        var parser = new ParameterIndexParser();
        var rawData = new byte[] { 0xFF, 0xF1, 0xF1, 0x12, 0x25, 0x35, 0x0E, 0x01, 0x4F }; // 错误的帧头

        // Act
        var result = parser.ParseFrame(rawData);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("无效的帧头", result.ErrorMessage);
    }
}

/// <summary>
/// 自定义数据解析器测试
/// </summary>
public class CustomDataParserTests
{
    [Fact]
    public void CanParse_SystemStatusFrame_ReturnsTrue()
    {
        // Arrange
        var parser = new CustomDataParser();
        var rawData = new byte[] { 0x7E, 0xF1, 0x00, 0xA1, 0x17, 0x23, 0x60 };

        // Act
        var canParse = parser.CanParse(rawData);

        // Assert
        Assert.True(canParse);
    }

    [Fact]
    public void CanParse_ParameterReadFrame_ReturnsFalse()
    {
        // Arrange
        var parser = new CustomDataParser();
        var rawData = new byte[] { 0x7E, 0xF1, 0xF1, 0x12, 0x25, 0x35, 0x0E };

        // Act
        var canParse = parser.CanParse(rawData);

        // Assert
        Assert.False(canParse);
    }

    [Fact]
    public void ParseFrame_TooShortFrame_ReturnsInvalidResult()
    {
        // Arrange
        var parser = new CustomDataParser();
        var rawData = new byte[] { 0x7E, 0xF1 }; // 太短的帧

        // Act
        var result = parser.ParseFrame(rawData);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("长度不足", result.ErrorMessage);
    }
}