# 串口通信模块开发文档

## 1. 功能需求描述

### 1.1 业务背景
串口通信模块是AirMonitor系统的核心组件，负责通过RS485总线与空调设备进行实时通信。系统采用主从轮询架构，主设备（通常为外机F1）通过RS485总线轮询各从设备，收集运行参数、状态信息，并执行控制命令。该模块需要支持高频数据采集、多设备并发通信、协议解析和错误处理。

### 1.2 功能范围
- **RS485总线管理**：串口连接管理、设备发现、连接状态监控
- **通信协议支持**：支持两种数据帧格式（参数索引格式、自定义数据格式）
- **数据帧处理**：数据帧构建、解析、校验（Modbus CRC16）
- **设备轮询机制**：主从轮询调度、超时处理、重试机制
- **数据缓存管理**：高频数据缓存、批量处理、内存优化
- **错误处理**：通信异常处理、设备离线检测、自动重连
- **性能优化**：异步处理、线程安全、资源管理

### 1.3 用户故事
- **作为Windows服务**，我需要稳定地与RS485总线上的设备通信，确保数据采集的连续性和可靠性
- **作为协议解析器**，我需要正确解析两种不同格式的数据帧，提取参数值和状态信息
- **作为设备管理器**，我需要动态发现总线上的设备，监控设备在线状态，处理设备上下线事件
- **作为数据采集服务**，我需要高效处理高频数据，避免数据丢失，确保实时性要求

## 2. 技术实现方案

### 2.1 架构设计
串口通信模块采用分层架构设计：

```
┌─────────────────────────────────────────────────┐
│              Windows Service 层                 │
│        (AirMonitor.WindowsService)              │
├─────────────────────────────────────────────────┤
│              串口通信服务层                      │
│    SerialCommunicationService (业务协调)        │
├─────────────────────────────────────────────────┤
│              协议处理层                          │
│  ProtocolManager + Parser (协议解析)            │
├─────────────────────────────────────────────────┤
│              设备管理层                          │
│   DeviceManager (设备发现和状态管理)            │
├─────────────────────────────────────────────────┤
│              串口驱动层                          │
│    SerialPortManager (底层串口操作)             │
└─────────────────────────────────────────────────┘
```

### 2.2 技术选型
- **串口通信**：System.IO.Ports.SerialPort
- **异步处理**：async/await + Task.Run
- **线程安全**：ConcurrentQueue、SemaphoreSlim、lock
- **配置管理**：IOptions<SerialPortSettings>
- **日志记录**：Serilog 结构化日志
- **依赖注入**：Microsoft.Extensions.DependencyInjection
- **后台服务**：BackgroundService
- **数据缓存**：ConcurrentDictionary + MemoryCache
- **错误处理**：Polly 重试策略

### 2.3 设计模式
- **工厂模式**：协议解析器工厂 (ProtocolParserFactory)
- **策略模式**：不同协议格式的解析策略
- **观察者模式**：设备状态变化通知
- **单例模式**：串口管理器实例
- **命令模式**：设备控制命令封装
- **生产者-消费者模式**：数据采集和处理

## 3. API 接口设计

### 3.1 核心服务接口

#### 3.1.1 串口通信服务接口
```csharp
public interface ISerialCommunicationService
{
    // 连接管理
    Task<bool> StartAsync(CancellationToken cancellationToken = default);
    Task StopAsync(CancellationToken cancellationToken = default);
    Task<bool> IsConnectedAsync();
    
    // 设备通信
    Task<byte[]?> SendCommandAsync(byte targetAddress, byte commandCode, byte[] data, 
        TimeSpan? timeout = null, CancellationToken cancellationToken = default);
    Task<ParseResult?> SendAndParseAsync(byte targetAddress, byte commandCode, byte[] data,
        CancellationToken cancellationToken = default);
    
    // 设备发现
    Task<IEnumerable<DeviceInfo>> DiscoverDevicesAsync(CancellationToken cancellationToken = default);
    Task<DeviceInfo?> GetDeviceInfoAsync(byte address, CancellationToken cancellationToken = default);
    
    // 事件通知
    event EventHandler<DeviceDataReceivedEventArgs> DeviceDataReceived;
    event EventHandler<DeviceStatusChangedEventArgs> DeviceStatusChanged;
    event EventHandler<CommunicationErrorEventArgs> CommunicationError;
}
```

#### 3.1.2 协议管理器接口
```csharp
public interface IProtocolManager
{
    // 数据帧构建
    byte[] BuildFrame(byte sourceAddress, byte targetAddress, byte commandCode, byte[] data);
    byte[] BuildParameterReadFrame(byte targetAddress, byte parameterIndex, byte count);
    byte[] BuildControlFrame(byte targetAddress, byte[] controlData);
    
    // 数据帧解析
    ParseResult ParseFrame(byte[] rawData);
    bool ValidateFrame(byte[] frame);
    ushort CalculateCRC16(byte[] data, int start, int length);
    
    // 协议格式检测
    ProtocolFormat DetectFormat(byte[] rawData);
    IProtocolParser GetParser(ProtocolFormat format);
}
```

#### 3.1.3 设备管理器接口
```csharp
public interface IDeviceManager
{
    // 设备注册和管理
    Task RegisterDeviceAsync(DeviceInfo device);
    Task UnregisterDeviceAsync(byte address);
    Task<DeviceInfo?> GetDeviceAsync(byte address);
    Task<IEnumerable<DeviceInfo>> GetAllDevicesAsync();
    
    // 设备状态管理
    Task UpdateDeviceStatusAsync(byte address, DeviceStatus status);
    Task<DeviceStatus> GetDeviceStatusAsync(byte address);
    Task<bool> IsDeviceOnlineAsync(byte address);
    
    // 设备轮询管理
    Task StartPollingAsync(CancellationToken cancellationToken = default);
    Task StopPollingAsync();
    Task<TimeSpan> GetPollingIntervalAsync(byte address);
    Task SetPollingIntervalAsync(byte address, TimeSpan interval);
}
```

### 3.2 数据传输对象 (DTO)

#### 3.2.1 设备信息DTO
```csharp
public class DeviceInfo
{
    public byte Address { get; set; }
    public DeviceType DeviceType { get; set; }
    public string? SerialNumber { get; set; }
    public string? ModelNumber { get; set; }
    public DeviceStatus Status { get; set; }
    public DateTimeOffset LastCommunication { get; set; }
    public TimeSpan PollingInterval { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}
```

#### 3.2.2 解析结果DTO
```csharp
public class ParseResult
{
    public bool IsValid { get; set; }
    public ProtocolFormat Format { get; set; }
    public byte SourceAddress { get; set; }
    public byte TargetAddress { get; set; }
    public byte CommandCode { get; set; }
    public byte DataLength { get; set; }
    public byte[]? RawData { get; set; }
    public ParameterData[]? Parameters { get; set; }
    public Dictionary<string, object>? CustomData { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTimeOffset Timestamp { get; set; }
}
```

#### 3.2.3 参数数据DTO
```csharp
public class ParameterData
{
    public byte Index { get; set; }
    public ushort RawValue { get; set; }
    public object? ParsedValue { get; set; }
    public ParameterValueFormat Format { get; set; }
    public string? Unit { get; set; }
    public DataQuality Quality { get; set; }
}
```

### 3.3 事件参数定义
```csharp
public class DeviceDataReceivedEventArgs : EventArgs
{
    public byte DeviceAddress { get; set; }
    public ParseResult ParseResult { get; set; }
    public DateTimeOffset Timestamp { get; set; }
}

public class DeviceStatusChangedEventArgs : EventArgs
{
    public byte DeviceAddress { get; set; }
    public DeviceStatus OldStatus { get; set; }
    public DeviceStatus NewStatus { get; set; }
    public DateTimeOffset Timestamp { get; set; }
}

public class CommunicationErrorEventArgs : EventArgs
{
    public byte? DeviceAddress { get; set; }
    public string ErrorMessage { get; set; }
    public Exception? Exception { get; set; }
    public DateTimeOffset Timestamp { get; set; }
}
```

## 4. 数据模型定义

### 4.1 配置模型
```csharp
public class SerialPortSettings
{
    public const string SectionName = "SerialPort";
    
    public string DefaultPortName { get; set; } = "COM1";
    public int BaudRate { get; set; } = 9600;
    public int DataBits { get; set; } = 8;
    public StopBits StopBits { get; set; } = StopBits.One;
    public Parity Parity { get; set; } = Parity.None;
    public int ReadTimeout { get; set; } = 5000;
    public int WriteTimeout { get; set; } = 5000;
    public int ReceiveBufferSize { get; set; } = 4096;
    public int SendBufferSize { get; set; } = 4096;
    public bool DtrEnable { get; set; } = false;
    public bool RtsEnable { get; set; } = false;
    public int RetryCount { get; set; } = 3;
    public int RetryInterval { get; set; } = 1000;
    public bool AutoReconnect { get; set; } = true;
    public int HeartbeatInterval { get; set; } = 30000;
}

public class CommunicationSettings
{
    public const string SectionName = "Communication";
    
    public int DefaultPollingInterval { get; set; } = 5000;
    public int DeviceDiscoveryTimeout { get; set; } = 10000;
    public int MaxConcurrentOperations { get; set; } = 10;
    public int DataCacheSize { get; set; } = 1000;
    public int BatchProcessingSize { get; set; } = 100;
    public bool EnableDataValidation { get; set; } = true;
    public bool EnablePerformanceLogging { get; set; } = false;
}
```

### 4.2 协议常量定义
```csharp
public static class ProtocolConstants
{
    // 帧头和基本结构
    public const byte FRAME_HEADER = 0x7E;
    public const int MIN_FRAME_LENGTH = 7;
    public const int CRC_LENGTH = 2;
    
    // 命令码定义
    public const byte CMD_PARAMETER_READ = 0x12;
    public const byte CMD_PARAMETER_WRITE = 0x13;
    public const byte CMD_SYSTEM_STATUS = 0xA1;
    public const byte CMD_DEVICE_INFO = 0xA2;
    public const byte CMD_HEARTBEAT = 0xFF;
    
    // 地址定义
    public const byte BROADCAST_ADDRESS = 0x00;
    public const byte MASTER_ADDRESS = 0xF1;
    
    // 协议格式标识
    public const byte FORMAT_PARAMETER_INDEX = 0x01;
    public const byte FORMAT_CUSTOM_DATA = 0x02;
}
```

## 5. 业务逻辑设计

### 5.1 串口通信服务实现

#### 5.1.1 核心服务类结构
```csharp
public class SerialCommunicationService : ISerialCommunicationService, IDisposable
{
    private readonly ISerialPortManager _serialPortManager;
    private readonly IProtocolManager _protocolManager;
    private readonly IDeviceManager _deviceManager;
    private readonly ILogger<SerialCommunicationService> _logger;
    private readonly SerialPortSettings _settings;
    private readonly SemaphoreSlim _communicationSemaphore;
    private readonly CancellationTokenSource _cancellationTokenSource;

    // 事件定义
    public event EventHandler<DeviceDataReceivedEventArgs>? DeviceDataReceived;
    public event EventHandler<DeviceStatusChangedEventArgs>? DeviceStatusChanged;
    public event EventHandler<CommunicationErrorEventArgs>? CommunicationError;
}
```

#### 5.1.2 连接管理逻辑
```csharp
public async Task<bool> StartAsync(CancellationToken cancellationToken = default)
{
    try
    {
        _logger.LogInformation("启动串口通信服务...");

        // 1. 初始化串口连接
        var connected = await _serialPortManager.ConnectAsync(_settings.DefaultPortName, cancellationToken);
        if (!connected)
        {
            _logger.LogError("串口连接失败: {PortName}", _settings.DefaultPortName);
            return false;
        }

        // 2. 启动数据接收监听
        _ = Task.Run(() => StartDataReceiveLoop(cancellationToken), cancellationToken);

        // 3. 启动设备轮询
        await _deviceManager.StartPollingAsync(cancellationToken);

        // 4. 启动设备发现
        _ = Task.Run(() => StartDeviceDiscovery(cancellationToken), cancellationToken);

        _logger.LogInformation("串口通信服务启动成功");
        return true;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "启动串口通信服务失败");
        return false;
    }
}
```

#### 5.1.3 数据发送逻辑
```csharp
public async Task<byte[]?> SendCommandAsync(byte targetAddress, byte commandCode, byte[] data,
    TimeSpan? timeout = null, CancellationToken cancellationToken = default)
{
    await _communicationSemaphore.WaitAsync(cancellationToken);
    try
    {
        // 1. 构建数据帧
        var frame = _protocolManager.BuildFrame(ProtocolConstants.MASTER_ADDRESS,
            targetAddress, commandCode, data);

        // 2. 发送数据帧
        var success = await _serialPortManager.SendAsync(frame, cancellationToken);
        if (!success)
        {
            _logger.LogWarning("发送数据帧失败: 目标地址={TargetAddress:X2}", targetAddress);
            return null;
        }

        // 3. 等待响应
        var responseTimeout = timeout ?? TimeSpan.FromMilliseconds(_settings.ReadTimeout);
        var response = await _serialPortManager.ReceiveAsync(responseTimeout, cancellationToken);

        // 4. 验证响应
        if (response != null && _protocolManager.ValidateFrame(response))
        {
            _logger.LogDebug("收到有效响应: 长度={Length}, 目标地址={TargetAddress:X2}",
                response.Length, targetAddress);
            return response;
        }

        _logger.LogWarning("未收到有效响应: 目标地址={TargetAddress:X2}", targetAddress);
        return null;
    }
    finally
    {
        _communicationSemaphore.Release();
    }
}
```

### 5.2 协议管理器实现

#### 5.2.1 数据帧构建
```csharp
public byte[] BuildFrame(byte sourceAddress, byte targetAddress, byte commandCode, byte[] data)
{
    var frameLength = 5 + data.Length + 2; // 头码+源+目标+命令+长度+数据+CRC
    var frame = new byte[frameLength];

    frame[0] = ProtocolConstants.FRAME_HEADER;  // 头码
    frame[1] = sourceAddress;                   // 源地址
    frame[2] = targetAddress;                   // 目标地址
    frame[3] = commandCode;                     // 命令码
    frame[4] = (byte)(frameLength - 2);         // 长度（不包括头码和CRC）

    // 复制数据
    Array.Copy(data, 0, frame, 5, data.Length);

    // 计算并添加CRC16
    var crc = CalculateCRC16(frame, 1, frameLength - 3);
    frame[frameLength - 2] = (byte)(crc >> 8);
    frame[frameLength - 1] = (byte)(crc & 0xFF);

    return frame;
}
```

#### 5.2.2 协议格式检测
```csharp
public ProtocolFormat DetectFormat(byte[] rawData)
{
    if (rawData.Length < ProtocolConstants.MIN_FRAME_LENGTH)
        return ProtocolFormat.Unknown;

    var commandCode = rawData[3];

    // 根据命令码判断协议格式
    return commandCode switch
    {
        ProtocolConstants.CMD_PARAMETER_READ => ProtocolFormat.ParameterIndex,
        ProtocolConstants.CMD_PARAMETER_WRITE => ProtocolFormat.ParameterIndex,
        ProtocolConstants.CMD_SYSTEM_STATUS => ProtocolFormat.CustomData,
        ProtocolConstants.CMD_DEVICE_INFO => ProtocolFormat.CustomData,
        _ => ProtocolFormat.Unknown
    };
}
```

### 5.3 设备管理器实现

#### 5.3.1 设备轮询调度
```csharp
public async Task StartPollingAsync(CancellationToken cancellationToken = default)
{
    _logger.LogInformation("启动设备轮询服务");

    while (!cancellationToken.IsCancellationRequested)
    {
        try
        {
            var devices = await GetAllDevicesAsync();
            var pollingTasks = devices.Select(device =>
                PollDeviceAsync(device, cancellationToken)).ToArray();

            await Task.WhenAll(pollingTasks);

            // 等待下一轮轮询
            await Task.Delay(_communicationSettings.DefaultPollingInterval, cancellationToken);
        }
        catch (OperationCanceledException)
        {
            break;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设备轮询过程中发生错误");
            await Task.Delay(5000, cancellationToken); // 错误后等待5秒
        }
    }

    _logger.LogInformation("设备轮询服务已停止");
}
```

#### 5.3.2 单设备轮询逻辑
```csharp
private async Task PollDeviceAsync(DeviceInfo device, CancellationToken cancellationToken)
{
    try
    {
        // 1. 检查设备是否需要轮询
        if (DateTime.UtcNow - device.LastCommunication < device.PollingInterval)
            return;

        // 2. 发送心跳包检查设备状态
        var heartbeatData = new byte[] { 0x00 };
        var response = await _communicationService.SendCommandAsync(
            device.Address, ProtocolConstants.CMD_HEARTBEAT, heartbeatData,
            TimeSpan.FromSeconds(5), cancellationToken);

        // 3. 更新设备状态
        if (response != null)
        {
            await UpdateDeviceStatusAsync(device.Address, DeviceStatus.Normal);
            device.LastCommunication = DateTimeOffset.UtcNow;

            // 4. 读取设备参数（如果需要）
            await ReadDeviceParametersAsync(device, cancellationToken);
        }
        else
        {
            await UpdateDeviceStatusAsync(device.Address, DeviceStatus.Offline);
            _logger.LogWarning("设备离线: 地址={Address:X2}", device.Address);
        }
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "轮询设备失败: 地址={Address:X2}", device.Address);
        await UpdateDeviceStatusAsync(device.Address, DeviceStatus.Error);
    }
}
```

## 6. 单元测试计划

### 6.1 测试策略
- **单元测试覆盖率**: ≥ 90%
- **集成测试**: 串口通信端到端测试
- **性能测试**: 高频数据处理性能测试
- **可靠性测试**: 长时间运行稳定性测试
- **模拟测试**: 使用虚拟串口进行测试

### 6.2 核心测试用例

#### 6.2.1 协议解析测试
```csharp
[TestClass]
public class ProtocolManagerTests
{
    [TestMethod]
    public void BuildFrame_ValidInput_ReturnsCorrectFrame()
    {
        // Arrange
        var protocolManager = new ProtocolManager();
        var data = new byte[] { 0x35, 0x0E, 0x01, 0x4F };

        // Act
        var frame = protocolManager.BuildFrame(0xF1, 0xF1, 0x12, data);

        // Assert
        Assert.AreEqual(0x7E, frame[0]); // 头码
        Assert.AreEqual(0xF1, frame[1]); // 源地址
        Assert.AreEqual(0xF1, frame[2]); // 目标地址
        Assert.AreEqual(0x12, frame[3]); // 命令码
        Assert.IsTrue(protocolManager.ValidateFrame(frame)); // CRC校验
    }

    [TestMethod]
    public void ParseFrame_ParameterIndexFormat_ReturnsValidResult()
    {
        // Arrange
        var rawData = new byte[] { 0x7E, 0xF1, 0xF1, 0x12, 0x25, 0x35, 0x0E,
            0x01, 0x4F, 0x00, 0xE3, 0x40, 0xEC };
        var protocolManager = new ProtocolManager();

        // Act
        var result = protocolManager.ParseFrame(rawData);

        // Assert
        Assert.IsTrue(result.IsValid);
        Assert.AreEqual(ProtocolFormat.ParameterIndex, result.Format);
        Assert.AreEqual(0xF1, result.SourceAddress);
        Assert.AreEqual(0x12, result.CommandCode);
        Assert.IsNotNull(result.Parameters);
    }
}
```

#### 6.2.2 串口通信测试
```csharp
[TestClass]
public class SerialCommunicationServiceTests
{
    private Mock<ISerialPortManager> _mockSerialPortManager;
    private Mock<IProtocolManager> _mockProtocolManager;
    private SerialCommunicationService _service;

    [TestInitialize]
    public void Setup()
    {
        _mockSerialPortManager = new Mock<ISerialPortManager>();
        _mockProtocolManager = new Mock<IProtocolManager>();
        _service = new SerialCommunicationService(
            _mockSerialPortManager.Object,
            _mockProtocolManager.Object,
            Mock.Of<IDeviceManager>(),
            Mock.Of<ILogger<SerialCommunicationService>>(),
            Options.Create(new SerialPortSettings()));
    }

    [TestMethod]
    public async Task SendCommandAsync_ValidCommand_ReturnsResponse()
    {
        // Arrange
        var expectedFrame = new byte[] { 0x7E, 0xF1, 0xF1, 0x12, 0x05, 0x00, 0x40, 0xEC };
        var expectedResponse = new byte[] { 0x7E, 0xF1, 0xF1, 0x12, 0x25, 0x35, 0x0E };

        _mockProtocolManager.Setup(x => x.BuildFrame(It.IsAny<byte>(), It.IsAny<byte>(),
            It.IsAny<byte>(), It.IsAny<byte[]>())).Returns(expectedFrame);
        _mockSerialPortManager.Setup(x => x.SendAsync(expectedFrame, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _mockSerialPortManager.Setup(x => x.ReceiveAsync(It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);
        _mockProtocolManager.Setup(x => x.ValidateFrame(expectedResponse)).Returns(true);

        // Act
        var result = await _service.SendCommandAsync(0xF1, 0x12, new byte[] { 0x00 });

        // Assert
        Assert.IsNotNull(result);
        CollectionAssert.AreEqual(expectedResponse, result);
    }
}
```

## 7. 性能与安全考虑

### 7.1 性能要求
- **数据处理延迟**: < 50ms
- **并发连接数**: 支持100+设备
- **数据吞吐量**: > 1000条/秒
- **内存使用**: < 100MB
- **CPU使用率**: < 30%

### 7.2 性能优化策略
```csharp
// 1. 数据缓存优化
public class DataCacheManager
{
    private readonly ConcurrentDictionary<byte, DeviceDataCache> _deviceCaches;
    private readonly MemoryCache _memoryCache;
    private readonly Timer _cleanupTimer;

    public void CacheDeviceData(byte deviceAddress, ParseResult data)
    {
        var cache = _deviceCaches.GetOrAdd(deviceAddress, _ => new DeviceDataCache());
        cache.AddData(data);

        // 批量处理优化
        if (cache.Count >= _batchSize)
        {
            _ = Task.Run(() => ProcessBatchData(deviceAddress, cache.GetBatch()));
        }
    }
}

// 2. 异步批量处理
private async Task ProcessBatchData(byte deviceAddress, IEnumerable<ParseResult> dataList)
{
    try
    {
        var monitoringData = dataList.Select(ConvertToMonitoringData).ToList();
        await _dataRepository.SaveBatchAsync(monitoringData);

        // 通知实时数据更新
        await _signalRHub.Clients.All.SendAsync("DeviceDataUpdated", deviceAddress, dataList);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "批量处理设备数据失败: 设备地址={DeviceAddress:X2}", deviceAddress);
    }
}
```

### 7.3 安全措施
- **数据验证**: CRC16校验确保数据完整性
- **访问控制**: 基于角色的设备访问权限
- **异常处理**: 完善的异常捕获和恢复机制
- **资源保护**: 连接池和资源限制
- **日志审计**: 详细的操作日志记录

### 7.4 错误处理策略
```csharp
public class CommunicationErrorHandler
{
    private readonly ILogger _logger;
    private readonly Dictionary<byte, int> _deviceErrorCounts;

    public async Task<bool> HandleCommunicationError(byte deviceAddress, Exception exception)
    {
        _deviceErrorCounts.TryGetValue(deviceAddress, out var errorCount);
        errorCount++;
        _deviceErrorCounts[deviceAddress] = errorCount;

        _logger.LogWarning("设备通信错误: 地址={Address:X2}, 错误次数={Count}, 异常={Exception}",
            deviceAddress, errorCount, exception.Message);

        // 错误次数超过阈值时标记设备离线
        if (errorCount >= 3)
        {
            await _deviceManager.UpdateDeviceStatusAsync(deviceAddress, DeviceStatus.Error);
            _deviceErrorCounts.Remove(deviceAddress);
            return false;
        }

        // 实施退避重试策略
        var delay = TimeSpan.FromSeconds(Math.Pow(2, errorCount));
        await Task.Delay(delay);
        return true;
    }
}
```
```
