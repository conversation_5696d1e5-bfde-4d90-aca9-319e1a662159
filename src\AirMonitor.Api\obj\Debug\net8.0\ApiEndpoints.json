[{"ContainingType": "AirMonitor.Api.Controllers.DevicesController", "Method": "GetDevices", "RelativePath": "api/Devices", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[AirMonitor.Core.Entities.Devices.Device, AirMonitor.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "AirMonitor.Api.Controllers.DevicesController", "Method": "CreateDevice", "RelativePath": "api/Devices", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "device", "Type": "AirMonitor.Core.Entities.Devices.Device", "IsRequired": true}], "ReturnTypes": [{"Type": "AirMonitor.Core.Entities.Devices.Device", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "AirMonitor.Api.Controllers.DevicesController", "Method": "GetDevice", "RelativePath": "api/Devices/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "AirMonitor.Core.Entities.Devices.Device", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "AirMonitor.Api.Controllers.DevicesController", "Method": "GetDeviceByAddress", "RelativePath": "api/Devices/address/{address}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "address", "Type": "System.Byte", "IsRequired": true}], "ReturnTypes": [{"Type": "AirMonitor.Core.Entities.Devices.Device", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "AirMonitor.Api.Controllers.DevicesController", "Method": "UpdateDeviceOnlineStatus", "RelativePath": "api/Devices/address/{address}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "address", "Type": "System.Byte", "IsRequired": true}, {"Name": "isOnline", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AirMonitor.Api.Controllers.DevicesController", "Method": "GetOnlineDevices", "RelativePath": "api/Devices/online", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[AirMonitor.Core.Entities.Devices.Device, AirMonitor.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "AirMonitor.Api.Controllers.DevicesController", "Method": "GetDevicesByType", "RelativePath": "api/Devices/type/{deviceType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[AirMonitor.Core.Entities.Devices.Device, AirMonitor.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "AirMonitor.Api.Controllers.MonitoringDataController", "Method": "SaveMonitoringData", "RelativePath": "api/MonitoringData", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "data", "Type": "AirMonitor.Core.Entities.Monitoring.MonitoringData", "IsRequired": true}], "ReturnTypes": [{"Type": "AirMonitor.Core.Entities.Monitoring.MonitoringData", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "AirMonitor.Api.Controllers.MonitoringDataController", "Method": "GetLatestByDevice", "RelativePath": "api/MonitoringData/device/{deviceId}/latest", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int32", "IsRequired": true}, {"Name": "count", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[AirMonitor.Core.Entities.Monitoring.MonitoringData, AirMonitor.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "AirMonitor.Api.Controllers.MonitoringDataController", "Method": "GetLatestParameterValue", "RelativePath": "api/MonitoringData/device/{deviceId}/parameter/{parameterIndex}/latest", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int32", "IsRequired": true}, {"Name": "parameterIndex", "Type": "System.Byte", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AirMonitor.Api.Controllers.MonitoringDataController", "Method": "GetByTimeRange", "RelativePath": "api/MonitoringData/device/{deviceId}/range", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int32", "IsRequired": true}, {"Name": "start", "Type": "System.DateTimeOffset", "IsRequired": false}, {"Name": "end", "Type": "System.DateTimeOffset", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[AirMonitor.Core.Entities.Monitoring.MonitoringData, AirMonitor.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Program+<>c__DisplayClass0_0", "Method": "<<Main>$>b__3", "RelativePath": "api/status", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "Tags": ["System"], "EndpointName": "GetStatus"}, {"ContainingType": "AirMonitor.Api.Controllers.SystemController", "Method": "GetConfiguration", "RelativePath": "api/System/config", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [], "Tags": ["System"]}, {"ContainingType": "AirMonitor.Api.Controllers.SystemController", "Method": "HealthCheck", "RelativePath": "api/System/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [], "Tags": ["System"]}, {"ContainingType": "AirMonitor.Api.Controllers.SystemController", "Method": "GetSystemInfo", "RelativePath": "api/System/info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [], "Tags": ["System"]}]