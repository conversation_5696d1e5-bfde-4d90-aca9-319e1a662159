2025-06-08 11:50:17.798 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-08 11:50:18.098 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:50:18.102 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:50:18.171 +08:00 [INF] No migrations were found in assembly 'AirMonitor.Infrastructure'. A migration needs to be added before the database can be updated.
2025-06-08 11:51:24.340 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-08 11:51:24.589 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:51:24.593 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:51:24.653 +08:00 [INF] No migrations were found in assembly 'AirMonitor.Infrastructure'. A migration needs to be added before the database can be updated.
2025-06-08 11:52:40.738 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:52:40.758 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:52:50.134 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-08 11:52:50.374 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:52:50.377 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:52:50.513 +08:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-06-08 11:52:50.518 +08:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-08 11:52:50.522 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-06-08 11:52:50.535 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsLock" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK___EFMigrationsLock" PRIMARY KEY,
    "Timestamp" TEXT NOT NULL
);
2025-06-08 11:52:50.539 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-06-08 03:52:50.5367246+00:00');
SELECT changes();
2025-06-08 11:52:50.564 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-06-08 11:52:50.574 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-06-08 11:52:50.576 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-08 11:52:50.581 +08:00 [INF] Applying migration '20250608035241_InitialCreate'.
2025-06-08 11:52:50.594 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_Devices" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_Devices" PRIMARY KEY AUTOINCREMENT,

    -- RS485设备地址
    "DeviceAddress" INTEGER NOT NULL,

    -- 设备类型
    "DeviceType" INTEGER NOT NULL,

    -- 设备序列号
    "SerialNumber" TEXT NULL,

    -- 设备型号
    "ModelNumber" TEXT NOT NULL,

    -- 固件版本
    "FirmwareVersion" TEXT NOT NULL,

    -- 设备状态
    "Status" INTEGER NOT NULL,

    "LastCommunication" TEXT NOT NULL,

    "IsOnline" INTEGER NOT NULL,

    -- 设备位置
    "Location" TEXT NULL,

    -- 设备描述
    "Description" TEXT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL
);
2025-06-08 11:52:50.597 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_ParameterTemplates" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_ParameterTemplates" PRIMARY KEY AUTOINCREMENT,

    -- 模板名称
    "Name" TEXT NOT NULL,

    -- 模板描述
    "Description" TEXT NOT NULL,

    -- 适用的设备类型
    "ApplicableDeviceType" INTEGER NOT NULL,

    -- 设备型号匹配模式
    "ModelPattern" TEXT NOT NULL,

    "IsActive" INTEGER NOT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL
);
2025-06-08 11:52:50.598 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_Users" PRIMARY KEY AUTOINCREMENT,

    -- 用户名
    "Username" TEXT NOT NULL,

    -- 邮箱
    "Email" TEXT NOT NULL,

    -- 密码哈希
    "PasswordHash" TEXT NOT NULL,

    -- 用户角色
    "Role" INTEGER NOT NULL,

    "IsActive" INTEGER NOT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL,

    "LastLoginAt" TEXT NULL
);
2025-06-08 11:52:50.599 +08:00 [ERR] Failed executing DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_CommunicationLogs" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_CommunicationLogs" PRIMARY KEY AUTOINCREMENT,

    "DeviceId" INTEGER NOT NULL,

    -- 通信类型
    "CommunicationType" INTEGER NOT NULL,

    -- 协议格式
    "ProtocolFormat" INTEGER NOT NULL,

    -- 源地址
    "SourceAddress" INTEGER NOT NULL,

    -- 目标地址
    "TargetAddress" INTEGER NOT NULL,

    -- 命令码
    "CommandCode" INTEGER NOT NULL,

    -- 原始数据
    "RawData" BLOB NOT NULL,

    -- 解析后的数据JSON
    "ParsedData" nvarchar(max) NULL,

    "IsSuccessful" INTEGER NOT NULL,

    -- 错误消息
    "ErrorMessage" TEXT NULL,

    "Timestamp" TEXT NOT NULL,
    CONSTRAINT "FK_AM_CommunicationLogs_AM_Devices_DeviceId" FOREIGN KEY ("DeviceId") REFERENCES "AM_Devices" ("Id") ON DELETE CASCADE
);
2025-06-08 11:54:54.057 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-08 11:54:54.300 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:54:54.304 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:54:54.414 +08:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-08 11:54:54.425 +08:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-06-08 11:54:54.431 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-06-08 03:54:54.4285336+00:00');
SELECT changes();
2025-06-08 11:54:54.455 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-06-08 11:54:54.461 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-06-08 11:54:54.464 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-08 11:54:54.469 +08:00 [INF] Applying migration '20250608035241_InitialCreate'.
2025-06-08 11:54:54.484 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_Devices" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_Devices" PRIMARY KEY AUTOINCREMENT,

    -- RS485设备地址
    "DeviceAddress" INTEGER NOT NULL,

    -- 设备类型
    "DeviceType" INTEGER NOT NULL,

    -- 设备序列号
    "SerialNumber" TEXT NULL,

    -- 设备型号
    "ModelNumber" TEXT NOT NULL,

    -- 固件版本
    "FirmwareVersion" TEXT NOT NULL,

    -- 设备状态
    "Status" INTEGER NOT NULL,

    "LastCommunication" TEXT NOT NULL,

    "IsOnline" INTEGER NOT NULL,

    -- 设备位置
    "Location" TEXT NULL,

    -- 设备描述
    "Description" TEXT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL
);
2025-06-08 11:54:54.485 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_ParameterTemplates" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_ParameterTemplates" PRIMARY KEY AUTOINCREMENT,

    -- 模板名称
    "Name" TEXT NOT NULL,

    -- 模板描述
    "Description" TEXT NOT NULL,

    -- 适用的设备类型
    "ApplicableDeviceType" INTEGER NOT NULL,

    -- 设备型号匹配模式
    "ModelPattern" TEXT NOT NULL,

    "IsActive" INTEGER NOT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL
);
2025-06-08 11:54:54.487 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_Users" PRIMARY KEY AUTOINCREMENT,

    -- 用户名
    "Username" TEXT NOT NULL,

    -- 邮箱
    "Email" TEXT NOT NULL,

    -- 密码哈希
    "PasswordHash" TEXT NOT NULL,

    -- 用户角色
    "Role" INTEGER NOT NULL,

    "IsActive" INTEGER NOT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL,

    "LastLoginAt" TEXT NULL
);
2025-06-08 11:54:54.489 +08:00 [ERR] Failed executing DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_CommunicationLogs" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_CommunicationLogs" PRIMARY KEY AUTOINCREMENT,

    "DeviceId" INTEGER NOT NULL,

    -- 通信类型
    "CommunicationType" INTEGER NOT NULL,

    -- 协议格式
    "ProtocolFormat" INTEGER NOT NULL,

    -- 源地址
    "SourceAddress" INTEGER NOT NULL,

    -- 目标地址
    "TargetAddress" INTEGER NOT NULL,

    -- 命令码
    "CommandCode" INTEGER NOT NULL,

    -- 原始数据
    "RawData" BLOB NOT NULL,

    -- 解析后的数据JSON
    "ParsedData" nvarchar(max) NULL,

    "IsSuccessful" INTEGER NOT NULL,

    -- 错误消息
    "ErrorMessage" TEXT NULL,

    "Timestamp" TEXT NOT NULL,
    CONSTRAINT "FK_AM_CommunicationLogs_AM_Devices_DeviceId" FOREIGN KEY ("DeviceId") REFERENCES "AM_Devices" ("Id") ON DELETE CASCADE
);
2025-06-08 11:56:20.370 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:56:20.390 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:56:20.666 +08:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-06-08 11:56:20.674 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-08 11:56:50.456 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:56:50.476 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:57:00.345 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-08 11:57:00.615 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:57:00.618 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:57:00.685 +08:00 [INF] No migrations were found in assembly 'AirMonitor.Infrastructure'. A migration needs to be added before the database can be updated.
2025-06-08 11:57:00.700 +08:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-08 11:57:00.713 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-06-08 11:57:00.725 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-06-08 03:57:00.7165764+00:00');
SELECT changes();
2025-06-08 11:57:00.781 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-06-08 11:57:00.788 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-06-08 11:57:00.791 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-08 11:57:00.795 +08:00 [INF] No migrations were applied. The database is already up to date.
2025-06-08 11:57:00.799 +08:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-06-08 11:57:00.803 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-08 11:57:00.953 +08:00 [ERR] Failed executing DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "AM_ParameterTemplates" AS "a")
2025-06-08 11:57:00.960 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'AirMonitor.Infrastructure.Data.AirMonitorDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: AM_ParameterTemplates'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: AM_ParameterTemplates'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-08 11:57:55.584 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-08 11:57:55.853 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:57:55.857 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:57:55.920 +08:00 [INF] No migrations were found in assembly 'AirMonitor.Infrastructure'. A migration needs to be added before the database can be updated.
2025-06-08 11:57:55.935 +08:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-08 11:57:55.948 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-06-08 11:57:55.955 +08:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-06-08 03:57:55.9516722+00:00');
SELECT changes();
2025-06-08 11:57:56.011 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-06-08 11:57:56.018 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-06-08 11:57:56.021 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-08 11:57:56.025 +08:00 [INF] No migrations were applied. The database is already up to date.
2025-06-08 11:57:56.029 +08:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-06-08 11:57:56.032 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-08 11:57:56.181 +08:00 [ERR] Failed executing DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "AM_ParameterTemplates" AS "a")
2025-06-08 11:57:56.188 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'AirMonitor.Infrastructure.Data.AirMonitorDbContext'.
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: AM_ParameterTemplates'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such table: AM_ParameterTemplates'.
   at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
   at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
   at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-08 11:58:19.696 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:58:19.716 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:58:19.844 +08:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-08 11:58:19.856 +08:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-06-08 11:58:19.862 +08:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-06-08 03:58:19.8585969+00:00');
SELECT changes();
2025-06-08 11:58:19.887 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-06-08 11:58:19.890 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-06-08 11:58:19.892 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-08 11:58:19.897 +08:00 [INF] Applying migration '20250608035650_InitialCreate'.
2025-06-08 11:58:19.913 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_Devices" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_Devices" PRIMARY KEY AUTOINCREMENT,

    -- RS485设备地址
    "DeviceAddress" INTEGER NOT NULL,

    -- 设备类型
    "DeviceType" INTEGER NOT NULL,

    -- 设备序列号
    "SerialNumber" TEXT NULL,

    -- 设备型号
    "ModelNumber" TEXT NOT NULL,

    -- 固件版本
    "FirmwareVersion" TEXT NOT NULL,

    -- 设备状态
    "Status" INTEGER NOT NULL,

    "LastCommunication" TEXT NOT NULL,

    "IsOnline" INTEGER NOT NULL,

    -- 设备位置
    "Location" TEXT NULL,

    -- 设备描述
    "Description" TEXT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL
);
2025-06-08 11:58:19.914 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_ParameterTemplates" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_ParameterTemplates" PRIMARY KEY AUTOINCREMENT,

    -- 模板名称
    "Name" TEXT NOT NULL,

    -- 模板描述
    "Description" TEXT NOT NULL,

    -- 适用的设备类型
    "ApplicableDeviceType" INTEGER NOT NULL,

    -- 设备型号匹配模式
    "ModelPattern" TEXT NOT NULL,

    "IsActive" INTEGER NOT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL
);
2025-06-08 11:58:19.915 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_Users" PRIMARY KEY AUTOINCREMENT,

    -- 用户名
    "Username" TEXT NOT NULL,

    -- 邮箱
    "Email" TEXT NOT NULL,

    -- 密码哈希
    "PasswordHash" TEXT NOT NULL,

    -- 用户角色
    "Role" INTEGER NOT NULL,

    "IsActive" INTEGER NOT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL,

    "LastLoginAt" TEXT NULL
);
2025-06-08 11:58:19.916 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_CommunicationLogs" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_CommunicationLogs" PRIMARY KEY AUTOINCREMENT,

    "DeviceId" INTEGER NOT NULL,

    -- 通信类型
    "CommunicationType" INTEGER NOT NULL,

    -- 协议格式
    "ProtocolFormat" INTEGER NOT NULL,

    -- 源地址
    "SourceAddress" INTEGER NOT NULL,

    -- 目标地址
    "TargetAddress" INTEGER NOT NULL,

    -- 命令码
    "CommandCode" INTEGER NOT NULL,

    -- 原始数据
    "RawData" BLOB NOT NULL,

    -- 解析后的数据JSON
    "ParsedData" TEXT NULL,

    "IsSuccessful" INTEGER NOT NULL,

    -- 错误消息
    "ErrorMessage" TEXT NULL,

    "Timestamp" TEXT NOT NULL,
    CONSTRAINT "FK_AM_CommunicationLogs_AM_Devices_DeviceId" FOREIGN KEY ("DeviceId") REFERENCES "AM_Devices" ("Id") ON DELETE CASCADE
);
2025-06-08 11:58:19.918 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_ParameterDefinitions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_ParameterDefinitions" PRIMARY KEY AUTOINCREMENT,

    "ParameterTemplateId" INTEGER NOT NULL,

    -- 关联的命令码
    "CommandCode" INTEGER NOT NULL,

    -- 参数索引
    "ParameterIndex" INTEGER NOT NULL,

    -- 参数名称
    "Name" TEXT NOT NULL,

    -- 参数描述
    "Description" TEXT NOT NULL,

    -- 参数类别
    "Category" TEXT NOT NULL,

    -- 数据类型
    "DataType" TEXT NOT NULL,

    -- 参数值格式
    "ValueFormat" INTEGER NOT NULL,

    -- 格式配置JSON
    "FormatConfiguration" TEXT NULL,

    -- 参数单位
    "Unit" TEXT NOT NULL,

    -- 最小值
    "MinValue" TEXT NULL,

    -- 最大值
    "MaxValue" TEXT NULL,

    "IsReadOnly" INTEGER NOT NULL,

    "DisplayOrder" INTEGER NOT NULL,

    "IsActive" INTEGER NOT NULL,
    CONSTRAINT "FK_AM_ParameterDefinitions_AM_ParameterTemplates_ParameterTemplateId" FOREIGN KEY ("ParameterTemplateId") REFERENCES "AM_ParameterTemplates" ("Id") ON DELETE CASCADE
);
2025-06-08 11:58:19.919 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_UserDevicePermissions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_UserDevicePermissions" PRIMARY KEY AUTOINCREMENT,

    "UserId" INTEGER NOT NULL,

    "DeviceId" INTEGER NOT NULL,

    -- 权限级别
    "PermissionLevel" INTEGER NOT NULL,

    "GrantedAt" TEXT NOT NULL,

    "ExpiresAt" TEXT NULL,

    "IsActive" INTEGER NOT NULL,
    CONSTRAINT "FK_AM_UserDevicePermissions_AM_Devices_DeviceId" FOREIGN KEY ("DeviceId") REFERENCES "AM_Devices" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_AM_UserDevicePermissions_AM_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "AM_Users" ("Id") ON DELETE CASCADE
);
2025-06-08 11:58:19.920 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_DeviceParameters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_DeviceParameters" PRIMARY KEY AUTOINCREMENT,

    "DeviceId" INTEGER NOT NULL,

    "ParameterDefinitionId" INTEGER NOT NULL,

    "IsEnabled" INTEGER NOT NULL,

    -- 自定义名称
    "CustomName" TEXT NULL,

    -- 自定义配置JSON
    "CustomConfiguration" TEXT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL,
    CONSTRAINT "FK_AM_DeviceParameters_AM_Devices_DeviceId" FOREIGN KEY ("DeviceId") REFERENCES "AM_Devices" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_AM_DeviceParameters_AM_ParameterDefinitions_ParameterDefinitionId" FOREIGN KEY ("ParameterDefinitionId") REFERENCES "AM_ParameterDefinitions" ("Id") ON DELETE CASCADE
);
2025-06-08 11:58:19.921 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_ParameterPermissions" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_ParameterPermissions" PRIMARY KEY AUTOINCREMENT,

    -- 用户角色
    "UserRole" INTEGER NOT NULL,

    "ParameterDefinitionId" INTEGER NOT NULL,

    -- 权限级别
    "PermissionLevel" INTEGER NOT NULL,

    "IsActive" INTEGER NOT NULL,

    "CreatedAt" TEXT NOT NULL,
    CONSTRAINT "FK_AM_ParameterPermissions_AM_ParameterDefinitions_ParameterDefinitionId" FOREIGN KEY ("ParameterDefinitionId") REFERENCES "AM_ParameterDefinitions" ("Id") ON DELETE CASCADE
);
2025-06-08 11:58:19.922 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_MonitoringData" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_MonitoringData" PRIMARY KEY AUTOINCREMENT,

    "DeviceId" INTEGER NOT NULL,

    "DeviceParameterId" INTEGER NOT NULL,

    -- 参数索引
    "ParameterIndex" INTEGER NOT NULL,

    -- 原始值
    "RawValue" INTEGER NOT NULL,

    -- 解析后的值JSON
    "ParsedValue" TEXT NULL,

    -- 数值型值
    "NumericValue" TEXT NULL,

    -- 字符串值
    "StringValue" TEXT NULL,

    "BooleanValue" INTEGER NULL,

    -- 数据质量
    "Quality" INTEGER NOT NULL,

    "Timestamp" TEXT NOT NULL,

    "CreatedAt" TEXT NOT NULL,
    CONSTRAINT "FK_AM_MonitoringData_AM_DeviceParameters_DeviceParameterId" FOREIGN KEY ("DeviceParameterId") REFERENCES "AM_DeviceParameters" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_AM_MonitoringData_AM_Devices_DeviceId" FOREIGN KEY ("DeviceId") REFERENCES "AM_Devices" ("Id") ON DELETE CASCADE
);
2025-06-08 11:58:19.923 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CommunicationLogs_CommandCode" ON "AM_CommunicationLogs" ("CommandCode");
2025-06-08 11:58:19.924 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CommunicationLogs_Device_Time" ON "AM_CommunicationLogs" ("DeviceId", "Timestamp");
2025-06-08 11:58:19.925 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CommunicationLogs_IsSuccessful" ON "AM_CommunicationLogs" ("IsSuccessful");
2025-06-08 11:58:19.926 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CommunicationLogs_Timestamp" ON "AM_CommunicationLogs" ("Timestamp");
2025-06-08 11:58:19.927 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_AM_DeviceParameters_ParameterDefinitionId" ON "AM_DeviceParameters" ("ParameterDefinitionId");
2025-06-08 11:58:19.928 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_DeviceParameters_Device_Parameter" ON "AM_DeviceParameters" ("DeviceId", "ParameterDefinitionId");
2025-06-08 11:58:19.929 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_DeviceParameters_IsEnabled" ON "AM_DeviceParameters" ("IsEnabled");
2025-06-08 11:58:19.930 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Devices_DeviceAddress" ON "AM_Devices" ("DeviceAddress");
2025-06-08 11:58:19.931 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Devices_DeviceType" ON "AM_Devices" ("DeviceType");
2025-06-08 11:58:19.932 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Devices_IsOnline" ON "AM_Devices" ("IsOnline");
2025-06-08 11:58:19.933 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Devices_SerialNumber" ON "AM_Devices" ("SerialNumber");
2025-06-08 11:58:19.933 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_AM_MonitoringData_DeviceParameterId" ON "AM_MonitoringData" ("DeviceParameterId");
2025-06-08 11:58:19.934 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_MonitoringData_CreatedAt" ON "AM_MonitoringData" ("CreatedAt");
2025-06-08 11:58:19.935 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_MonitoringData_Device_Parameter_Time" ON "AM_MonitoringData" ("DeviceId", "ParameterIndex", "Timestamp");
2025-06-08 11:58:19.936 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_MonitoringData_Quality" ON "AM_MonitoringData" ("Quality");
2025-06-08 11:58:19.937 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_MonitoringData_Timestamp" ON "AM_MonitoringData" ("Timestamp");
2025-06-08 11:58:19.938 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_AM_ParameterDefinitions_ParameterTemplateId" ON "AM_ParameterDefinitions" ("ParameterTemplateId");
2025-06-08 11:58:19.939 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ParameterDefinitions_Category" ON "AM_ParameterDefinitions" ("Category");
2025-06-08 11:58:19.940 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_ParameterDefinitions_Command_Index" ON "AM_ParameterDefinitions" ("CommandCode", "ParameterIndex");
2025-06-08 11:58:19.940 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ParameterDefinitions_IsActive" ON "AM_ParameterDefinitions" ("IsActive");
2025-06-08 11:58:19.941 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_AM_ParameterPermissions_ParameterDefinitionId" ON "AM_ParameterPermissions" ("ParameterDefinitionId");
2025-06-08 11:58:19.943 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ParameterPermissions_IsActive" ON "AM_ParameterPermissions" ("IsActive");
2025-06-08 11:58:19.944 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_ParameterPermissions_Role_Parameter" ON "AM_ParameterPermissions" ("UserRole", "ParameterDefinitionId");
2025-06-08 11:58:19.945 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ParameterTemplates_DeviceType_ModelPattern" ON "AM_ParameterTemplates" ("ApplicableDeviceType", "ModelPattern");
2025-06-08 11:58:19.946 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ParameterTemplates_Name" ON "AM_ParameterTemplates" ("Name");
2025-06-08 11:58:19.946 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_AM_UserDevicePermissions_DeviceId" ON "AM_UserDevicePermissions" ("DeviceId");
2025-06-08 11:58:19.948 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserDevicePermissions_ExpiresAt" ON "AM_UserDevicePermissions" ("ExpiresAt");
2025-06-08 11:58:19.948 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserDevicePermissions_IsActive" ON "AM_UserDevicePermissions" ("IsActive");
2025-06-08 11:58:19.949 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_UserDevicePermissions_User_Device" ON "AM_UserDevicePermissions" ("UserId", "DeviceId");
2025-06-08 11:58:19.950 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Email" ON "AM_Users" ("Email");
2025-06-08 11:58:19.951 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Users_IsActive" ON "AM_Users" ("IsActive");
2025-06-08 11:58:19.952 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Users_Role" ON "AM_Users" ("Role");
2025-06-08 11:58:19.953 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Users_Username" ON "AM_Users" ("Username");
2025-06-08 11:58:19.954 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250608035650_InitialCreate', '9.0.5');
2025-06-08 11:58:19.960 +08:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-06-08 11:58:31.269 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-08 11:58:31.543 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:58:31.547 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:58:31.631 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-08 11:58:31.695 +08:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-08 11:58:31.698 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-06-08 11:58:31.712 +08:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-06-08 03:58:31.7003858+00:00');
SELECT changes();
2025-06-08 11:58:31.739 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-06-08 11:58:31.747 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-06-08 11:58:31.750 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-08 11:58:31.755 +08:00 [INF] No migrations were applied. The database is already up to date.
2025-06-08 11:58:31.758 +08:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-06-08 11:58:31.759 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-08 11:58:31.910 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "AM_ParameterTemplates" AS "a")
2025-06-08 11:58:31.978 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[@p0='1', @p1='2025-06-08T03:58:31.9127778+00:00', @p2='适用于标准室外机的参数定义' (Nullable = false) (Size = 13), @p3='True', @p4='*' (Nullable = false) (Size = 1), @p5='标准室外机参数模板' (Nullable = false) (Size = 9), @p6='2025-06-08T03:58:31.9127920+00:00'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_ParameterTemplates" ("ApplicableDeviceType", "CreatedAt", "Description", "IsActive", "ModelPattern", "Name", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6)
RETURNING "Id";
2025-06-08 11:58:32.053 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='Capacity' (Nullable = false) (Size = 8), @p1='18', @p2='u16' (Nullable = false) (Size = 3), @p3='设备额定制冷/制热容量' (Nullable = false) (Size = 11), @p4='1', @p5='{"DataType":"u16","Multiplier":100,"DecimalPlaces":1}' (Size = 53), @p6='True', @p7='True', @p8=NULL (DbType = Decimal), @p9=NULL (DbType = Decimal), @p10='额定容量' (Nullable = false) (Size = 4), @p11='53', @p12='1', @p13='W' (Nullable = false) (Size = 1), @p14='1'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_ParameterDefinitions" ("Category", "CommandCode", "DataType", "Description", "DisplayOrder", "FormatConfiguration", "IsActive", "IsReadOnly", "MaxValue", "MinValue", "Name", "ParameterIndex", "ParameterTemplateId", "Unit", "ValueFormat")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14)
RETURNING "Id";
2025-06-08 11:58:32.055 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='Temperature' (Nullable = false) (Size = 11), @p1='18', @p2='s16' (Nullable = false) (Size = 3), @p3='板式换热器气体进口温度' (Nullable = false) (Size = 11), @p4='2', @p5='{"DataType":"s16","Multiplier":0.1,"DecimalPlaces":1}' (Size = 53), @p6='True', @p7='True', @p8=NULL (DbType = Decimal), @p9=NULL (DbType = Decimal), @p10='板换气进温度Tgi' (Nullable = false) (Size = 9), @p11='54', @p12='1', @p13='°C' (Nullable = false) (Size = 2), @p14='1'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_ParameterDefinitions" ("Category", "CommandCode", "DataType", "Description", "DisplayOrder", "FormatConfiguration", "IsActive", "IsReadOnly", "MaxValue", "MinValue", "Name", "ParameterIndex", "ParameterTemplateId", "Unit", "ValueFormat")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14)
RETURNING "Id";
2025-06-08 11:58:32.057 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='Status' (Nullable = false) (Size = 6), @p1='18', @p2='u16' (Nullable = false) (Size = 3), @p3='子机实际调频状态和保护状态' (Nullable = false) (Size = 13), @p4='3', @p5='{"BitDefinitions":[{"Name":"\u8C03\u9891\u72B6\u6001","StartBit":12,"BitCount":4,"EnumValues":["\u505C\u6B62","\u6545\u969C","\u6B8B\u4F59","\u5BB9\u91CF\u5206\u914D","\u5B9A\u9891\u5F85\u673A","\u542F\u52A8\u65F6\u5E8F","\u5173\u95ED\u5F85\u673A","\u5173\u95ED\u65F6\u5E8F","\u4E0D\u53EF\u5206\u914D\u9636\u6BB5"]},{"Name":"\u5B50\u673A\u80FD\u529B\u4FDD\u62A4","StartBit":7,"BitCount":1},{"Name":"\u53D8\u9891\u538B\u7F29\u673A\u8F6C\u901F\u4FDD\u62A4","StartBit":6,"BitCount":1},{"Name":"\u6A21\u5757\u6E29\u5EA6\u8FC7\u9AD8","StartBit":5,"BitCount":1},{"Name":"\u7535\u6D41\u8FC7\u9AD8","StartBit":4,"BitCount":1},{"Name":"Td\u8FC7\u9AD8","StartBit":3,"BitCount":1},{"Name":"\u538B\u7F29\u6BD4\u8FC7\u9AD8","StartBit":2,"BitCount":1},{"Name":"Ps\u8FC7\u4F4E","StartBit":1,"BitCount":1},{"Name":"Pd\u8FC7\u9AD8","StartBit":0,"BitCount":1}]}' (Size = 843), @p6='True', @p7='True', @p8=NULL (DbType = Decimal), @p9=NULL (DbType = Decimal), @p10='子机调频状态' (Nullable = false) (Size = 6), @p11='39', @p12='1', @p13='' (Nullable = false), @p14='2'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_ParameterDefinitions" ("Category", "CommandCode", "DataType", "Description", "DisplayOrder", "FormatConfiguration", "IsActive", "IsReadOnly", "MaxValue", "MinValue", "Name", "ParameterIndex", "ParameterTemplateId", "Unit", "ValueFormat")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14)
RETURNING "Id";
2025-06-08 11:58:32.058 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='System' (Nullable = false) (Size = 6), @p1='161', @p2='u8' (Nullable = false) (Size = 2), @p3='系统配置和模式标志位' (Nullable = false) (Size = 10), @p4='1', @p5='{"BitDefinitions":[{"Name":"\u652F\u6301\u5185\u673A\u8FC7\u51B7/\u70ED\u5EA6\u4FEE\u6B63\u6807\u5FD7","StartBit":7,"BitCount":1},{"Name":"\u662F\u5426\u5141\u8BB8\u81EA\u52A8\u5730\u5740\u8BBE\u5B9A","StartBit":6,"BitCount":1},{"Name":"\u5185\u673A\u5730\u5740\u7ADE\u4E89\u65B9\u5F0F","StartBit":5,"BitCount":1},{"Name":"\u7981\u6B62\u5BA4\u5185\u673AThermoOn\u8F6C\u6362\u6807\u5FD7","StartBit":4,"BitCount":1},{"Name":"26\u5EA6\u8282\u80FD\u9501\u5B9A","StartBit":3,"BitCount":1},{"Name":"\u7CFB\u7EDF\u6A21\u5F0F\u4F18\u5148\u7EA7","StartBit":0,"BitCount":3,"EnumValues":["\u901A\u5E38","\u5355\u51B7","\u5355\u70ED","\u51B7\u4F18\u5148","\u70ED\u4F18\u5148","\u5C11\u6570\u670D\u4ECE\u591A\u6570"]}]}' (Size = 706), @p6='True', @p7='True', @p8=NULL (DbType = Decimal), @p9=NULL (DbType = Decimal), @p10='系统标志位' (Nullable = false) (Size = 5), @p11='255', @p12='1', @p13='' (Nullable = false), @p14='2'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_ParameterDefinitions" ("Category", "CommandCode", "DataType", "Description", "DisplayOrder", "FormatConfiguration", "IsActive", "IsReadOnly", "MaxValue", "MinValue", "Name", "ParameterIndex", "ParameterTemplateId", "Unit", "ValueFormat")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14)
RETURNING "Id";
2025-06-08 11:58:32.060 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='Status' (Nullable = false) (Size = 6), @p1='161', @p2='u8' (Nullable = false) (Size = 2), @p3='室外机工作状态和运行模式' (Nullable = false) (Size = 12), @p4='2', @p5='{"BitDefinitions":[{"Name":"\u5BA4\u5916\u7CFB\u7EDF\u5DE5\u4F5C\u6A21\u5F0F","StartBit":6,"BitCount":2,"EnumValues":["\u505C\u6B62","\u5236\u51B7","\u5236\u70ED","\u5236\u70ED\u6C34"]},{"Name":"\u538B\u7F29\u673A\u8FD0\u8F6C\u72B6\u6001","StartBit":5,"BitCount":1},{"Name":"\u56DE\u6CB9\u72B6\u6001","StartBit":4,"BitCount":1},{"Name":"\u9664\u971C\u72B6\u6001","StartBit":3,"BitCount":1},{"Name":"\u9664\u971C\u51C6\u5907\u72B6\u6001","StartBit":2,"BitCount":1},{"Name":"\u56DB\u901A\u9600\u72B6\u6001","StartBit":1,"BitCount":1},{"Name":"\u5236\u70ED\u6A21\u5F0F\u56DE\u6CB9\u65B9\u5F0F","StartBit":0,"BitCount":1}]}' (Size = 619), @p6='True', @p7='True', @p8=NULL (DbType = Decimal), @p9=NULL (DbType = Decimal), @p10='室外机状态' (Nullable = false) (Size = 5), @p11='254', @p12='1', @p13='' (Nullable = false), @p14='2'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_ParameterDefinitions" ("Category", "CommandCode", "DataType", "Description", "DisplayOrder", "FormatConfiguration", "IsActive", "IsReadOnly", "MaxValue", "MinValue", "Name", "ParameterIndex", "ParameterTemplateId", "Unit", "ValueFormat")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14)
RETURNING "Id";
2025-06-08 11:58:32.061 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='Temperature' (Nullable = false) (Size = 11), @p1='161', @p2='u16' (Nullable = false) (Size = 3), @p3='外机系统高压饱和温度' (Nullable = false) (Size = 10), @p4='3', @p5='{"DataType":"u16","Multiplier":0.1,"DecimalPlaces":1}' (Size = 53), @p6='True', @p7='True', @p8=NULL (DbType = Decimal), @p9=NULL (DbType = Decimal), @p10='高压饱和温度' (Nullable = false) (Size = 6), @p11='253', @p12='1', @p13='°C' (Nullable = false) (Size = 2), @p14='1'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_ParameterDefinitions" ("Category", "CommandCode", "DataType", "Description", "DisplayOrder", "FormatConfiguration", "IsActive", "IsReadOnly", "MaxValue", "MinValue", "Name", "ParameterIndex", "ParameterTemplateId", "Unit", "ValueFormat")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14)
RETURNING "Id";
2025-06-08 11:58:32.062 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='Temperature' (Nullable = false) (Size = 11), @p1='161', @p2='u16' (Nullable = false) (Size = 3), @p3='外机系统低压饱和温度' (Nullable = false) (Size = 10), @p4='4', @p5='{"DataType":"u16","Multiplier":0.1,"DecimalPlaces":1}' (Size = 53), @p6='True', @p7='True', @p8=NULL (DbType = Decimal), @p9=NULL (DbType = Decimal), @p10='低压饱和温度' (Nullable = false) (Size = 6), @p11='252', @p12='1', @p13='°C' (Nullable = false) (Size = 2), @p14='1'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_ParameterDefinitions" ("Category", "CommandCode", "DataType", "Description", "DisplayOrder", "FormatConfiguration", "IsActive", "IsReadOnly", "MaxValue", "MinValue", "Name", "ParameterIndex", "ParameterTemplateId", "Unit", "ValueFormat")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14)
RETURNING "Id";
2025-06-08 11:58:32.064 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='2', @p1='2025-06-08T03:58:32.0455808+00:00', @p2='适用于标准室内机的参数定义' (Nullable = false) (Size = 13), @p3='True', @p4='*' (Nullable = false) (Size = 1), @p5='标准室内机参数模板' (Nullable = false) (Size = 9), @p6='2025-06-08T03:58:32.0455809+00:00'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_ParameterTemplates" ("ApplicableDeviceType", "CreatedAt", "Description", "IsActive", "ModelPattern", "Name", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6)
RETURNING "Id";
2025-06-08 11:58:32.075 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "AM_Users" AS "a"
    WHERE "a"."IsActive")
2025-06-08 11:58:32.089 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='Temperature' (Nullable = false) (Size = 11), @p1='18', @p2='s16' (Nullable = false) (Size = 3), @p3='室内环境温度' (Nullable = false) (Size = 6), @p4='1', @p5='{"DataType":"s16","Multiplier":0.1,"DecimalPlaces":1}' (Size = 53), @p6='True', @p7='True', @p8=NULL (DbType = Decimal), @p9=NULL (DbType = Decimal), @p10='室内温度' (Nullable = false) (Size = 4), @p11='1', @p12='2', @p13='°C' (Nullable = false) (Size = 2), @p14='1'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_ParameterDefinitions" ("Category", "CommandCode", "DataType", "Description", "DisplayOrder", "FormatConfiguration", "IsActive", "IsReadOnly", "MaxValue", "MinValue", "Name", "ParameterIndex", "ParameterTemplateId", "Unit", "ValueFormat")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14)
RETURNING "Id";
2025-06-08 11:58:32.091 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='Temperature' (Nullable = false) (Size = 11), @p1='18', @p2='s16' (Nullable = false) (Size = 3), @p3='用户设定的目标温度' (Nullable = false) (Size = 9), @p4='2', @p5='{"DataType":"s16","Multiplier":0.1,"DecimalPlaces":1}' (Size = 53), @p6='True', @p7='False', @p8='30' (Nullable = true), @p9='16' (Nullable = true), @p10='设定温度' (Nullable = false) (Size = 4), @p11='2', @p12='2', @p13='°C' (Nullable = false) (Size = 2), @p14='1'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_ParameterDefinitions" ("Category", "CommandCode", "DataType", "Description", "DisplayOrder", "FormatConfiguration", "IsActive", "IsReadOnly", "MaxValue", "MinValue", "Name", "ParameterIndex", "ParameterTemplateId", "Unit", "ValueFormat")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14)
RETURNING "Id";
2025-06-08 11:58:32.093 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='2025-06-08T03:58:32.0771187+00:00', @p1='<EMAIL>' (Nullable = false) (Size = 20), @p2='True', @p3=NULL (DbType = DateTimeOffset), @p4='AQAAAAEAACcQAAAAEK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8==' (Nullable = false) (Size = 84), @p5='1', @p6='2025-06-08T03:58:32.0771350+00:00', @p7='admin' (Nullable = false) (Size = 5)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_Users" ("CreatedAt", "Email", "IsActive", "LastLoginAt", "PasswordHash", "Role", "UpdatedAt", "Username")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id";
2025-06-08 11:58:32.094 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='2025-06-08T03:58:32.0771466+00:00', @p1='<EMAIL>' (Nullable = false) (Size = 17), @p2='True', @p3=NULL (DbType = DateTimeOffset), @p4='AQAAAAEAACcQAAAAEK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8==' (Nullable = false) (Size = 84), @p5='2', @p6='2025-06-08T03:58:32.0771467+00:00', @p7='rd_user' (Nullable = false) (Size = 7)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_Users" ("CreatedAt", "Email", "IsActive", "LastLoginAt", "PasswordHash", "Role", "UpdatedAt", "Username")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id";
2025-06-08 11:58:32.096 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='2025-06-08T03:58:32.0771468+00:00', @p1='<EMAIL>' (Nullable = false) (Size = 22), @p2='True', @p3=NULL (DbType = DateTimeOffset), @p4='AQAAAAEAACcQAAAAEK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8==' (Nullable = false) (Size = 84), @p5='3', @p6='2025-06-08T03:58:32.0771468+00:00', @p7='service_user' (Nullable = false) (Size = 12)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_Users" ("CreatedAt", "Email", "IsActive", "LastLoginAt", "PasswordHash", "Role", "UpdatedAt", "Username")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id";
2025-06-08 11:58:32.097 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[@p0='2025-06-08T03:58:32.0771470+00:00', @p1='<EMAIL>' (Nullable = false) (Size = 19), @p2='True', @p3=NULL (DbType = DateTimeOffset), @p4='AQAAAAEAACcQAAAAEK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8==' (Nullable = false) (Size = 84), @p5='4', @p6='2025-06-08T03:58:32.0771470+00:00', @p7='regular_user' (Nullable = false) (Size = 12)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AM_Users" ("CreatedAt", "Email", "IsActive", "LastLoginAt", "PasswordHash", "Role", "UpdatedAt", "Username")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id";
2025-06-08 11:58:32.134 +08:00 [INF] AirMonitor API 服务启动成功，监听地址: http://localhost:5236
2025-06-08 11:58:32.162 +08:00 [INF] Now listening on: http://localhost:5236
2025-06-08 11:58:32.163 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-08 11:58:32.164 +08:00 [INF] Hosting environment: Development
2025-06-08 11:58:32.164 +08:00 [INF] Content root path: D:\Project\08 AirMonitor\src\AirMonitor.Api
2025-06-08 11:58:55.216 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/index.html - null null
2025-06-08 11:58:55.258 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/index.html - 200 null text/html;charset=utf-8 43.2191ms
2025-06-08 11:58:56.425 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/swagger/v1/swagger.json - null null
2025-06-08 11:58:56.480 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 55.22ms
2025-06-08 11:59:02.383 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/swagger - null null
2025-06-08 11:59:02.386 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-08 11:59:02.396 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/swagger - 404 0 null 12.8558ms
2025-06-08 11:59:02.398 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5236/swagger, Response status code: 404
2025-06-08 11:59:30.057 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-08 11:59:30.421 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:59:30.426 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:59:30.521 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-08 11:59:30.584 +08:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-08 11:59:30.588 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-06-08 11:59:30.600 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-06-08 03:59:30.5907018+00:00');
SELECT changes();
2025-06-08 11:59:30.630 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-06-08 11:59:30.637 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-06-08 11:59:30.641 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-08 11:59:30.647 +08:00 [INF] No migrations were applied. The database is already up to date.
2025-06-08 11:59:30.650 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-06-08 11:59:30.652 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-08 11:59:30.795 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "AM_ParameterTemplates" AS "a")
2025-06-08 11:59:30.805 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "AM_Users" AS "a"
    WHERE "a"."IsActive")
2025-06-08 11:59:30.874 +08:00 [INF] AirMonitor API 服务启动成功，监听地址: https://localhost:7260, http://localhost:5236
2025-06-08 11:59:30.985 +08:00 [INF] Now listening on: https://localhost:7260
2025-06-08 11:59:30.985 +08:00 [INF] Now listening on: http://localhost:5236
2025-06-08 11:59:31.035 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-08 11:59:31.036 +08:00 [INF] Hosting environment: Development
2025-06-08 11:59:31.036 +08:00 [INF] Content root path: D:\Project\08 AirMonitor\src\AirMonitor.Api
2025-06-08 11:59:31.244 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7260/swagger - null null
2025-06-08 11:59:31.373 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7260/swagger - 404 0 null 134.467ms
2025-06-08 11:59:31.376 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7260/swagger, Response status code: 404
2025-06-08 11:59:48.464 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-08 11:59:48.829 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:59:48.833 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:59:48.928 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-08 11:59:48.991 +08:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-08 11:59:48.995 +08:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-06-08 11:59:49.000 +08:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-06-08 03:59:48.9973455+00:00');
SELECT changes();
2025-06-08 11:59:49.030 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-06-08 11:59:49.037 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-06-08 11:59:49.039 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-08 11:59:49.045 +08:00 [INF] No migrations were applied. The database is already up to date.
2025-06-08 11:59:49.048 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DELETE FROM "__EFMigrationsLock";
2025-06-08 11:59:49.049 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-08 11:59:49.198 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "AM_ParameterTemplates" AS "a")
2025-06-08 11:59:49.207 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM "AM_Users" AS "a"
    WHERE "a"."IsActive")
2025-06-08 11:59:49.277 +08:00 [INF] AirMonitor API 服务启动成功，监听地址: https://localhost:7260, http://localhost:5236
2025-06-08 11:59:49.384 +08:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5236: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
