﻿// <auto-generated />
using System;
using AirMonitor.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace AirMonitor.Infrastructure.Migrations
{
    [DbContext(typeof(AirMonitorDbContext))]
    partial class AirMonitorDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.5");

            modelBuilder.Entity("AirMonitor.Core.Entities.Devices.Device", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasComment("设备描述");

                    b.Property<byte>("DeviceAddress")
                        .HasColumnType("INTEGER")
                        .HasComment("RS485设备地址");

                    b.Property<int>("DeviceType")
                        .HasColumnType("INTEGER")
                        .HasComment("设备类型");

                    b.Property<string>("FirmwareVersion")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasComment("固件版本");

                    b.Property<bool>("IsOnline")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset>("LastCommunication")
                        .HasColumnType("TEXT");

                    b.Property<string>("Location")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT")
                        .HasComment("设备位置");

                    b.Property<string>("ModelNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasComment("设备型号");

                    b.Property<string>("SerialNumber")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("设备序列号");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER")
                        .HasComment("设备状态");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DeviceAddress")
                        .IsUnique()
                        .HasDatabaseName("IX_Devices_DeviceAddress");

                    b.HasIndex("DeviceType")
                        .HasDatabaseName("IX_Devices_DeviceType");

                    b.HasIndex("IsOnline")
                        .HasDatabaseName("IX_Devices_IsOnline");

                    b.HasIndex("SerialNumber")
                        .HasDatabaseName("IX_Devices_SerialNumber");

                    b.ToTable("AM_Devices", (string)null);
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Monitoring.CommunicationLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<byte>("CommandCode")
                        .HasColumnType("INTEGER")
                        .HasComment("命令码");

                    b.Property<int>("CommunicationType")
                        .HasColumnType("INTEGER")
                        .HasComment("通信类型");

                    b.Property<int>("DeviceId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT")
                        .HasComment("错误消息");

                    b.Property<bool>("IsSuccessful")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ParsedData")
                        .HasColumnType("TEXT")
                        .HasComment("解析后的数据JSON");

                    b.Property<int>("ProtocolFormat")
                        .HasColumnType("INTEGER")
                        .HasComment("协议格式");

                    b.Property<byte[]>("RawData")
                        .IsRequired()
                        .HasColumnType("BLOB")
                        .HasComment("原始数据");

                    b.Property<byte>("SourceAddress")
                        .HasColumnType("INTEGER")
                        .HasComment("源地址");

                    b.Property<byte>("TargetAddress")
                        .HasColumnType("INTEGER")
                        .HasComment("目标地址");

                    b.Property<DateTimeOffset>("Timestamp")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CommandCode")
                        .HasDatabaseName("IX_CommunicationLogs_CommandCode");

                    b.HasIndex("IsSuccessful")
                        .HasDatabaseName("IX_CommunicationLogs_IsSuccessful");

                    b.HasIndex("Timestamp")
                        .HasDatabaseName("IX_CommunicationLogs_Timestamp");

                    b.HasIndex("DeviceId", "Timestamp")
                        .HasDatabaseName("IX_CommunicationLogs_Device_Time");

                    b.ToTable("AM_CommunicationLogs", (string)null);
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Monitoring.MonitoringData", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("BooleanValue")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("DeviceId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("DeviceParameterId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("NumericValue")
                        .HasPrecision(18, 6)
                        .HasColumnType("TEXT")
                        .HasComment("数值型值");

                    b.Property<byte>("ParameterIndex")
                        .HasColumnType("INTEGER")
                        .HasComment("参数索引");

                    b.Property<string>("ParsedValue")
                        .HasColumnType("TEXT")
                        .HasComment("解析后的值JSON");

                    b.Property<int>("Quality")
                        .HasColumnType("INTEGER")
                        .HasComment("数据质量");

                    b.Property<ushort>("RawValue")
                        .HasColumnType("INTEGER")
                        .HasComment("原始值");

                    b.Property<string>("StringValue")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasComment("字符串值");

                    b.Property<DateTimeOffset>("Timestamp")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_MonitoringData_CreatedAt");

                    b.HasIndex("DeviceParameterId");

                    b.HasIndex("Quality")
                        .HasDatabaseName("IX_MonitoringData_Quality");

                    b.HasIndex("Timestamp")
                        .HasDatabaseName("IX_MonitoringData_Timestamp");

                    b.HasIndex("DeviceId", "ParameterIndex", "Timestamp")
                        .HasDatabaseName("IX_MonitoringData_Device_Parameter_Time");

                    b.ToTable("AM_MonitoringData", (string)null);
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Parameters.DeviceParameter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomConfiguration")
                        .HasColumnType("TEXT")
                        .HasComment("自定义配置JSON");

                    b.Property<string>("CustomName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("自定义名称");

                    b.Property<int>("DeviceId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ParameterDefinitionId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("IsEnabled")
                        .HasDatabaseName("IX_DeviceParameters_IsEnabled");

                    b.HasIndex("ParameterDefinitionId");

                    b.HasIndex("DeviceId", "ParameterDefinitionId")
                        .IsUnique()
                        .HasDatabaseName("IX_DeviceParameters_Device_Parameter");

                    b.ToTable("AM_DeviceParameters", (string)null);
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Parameters.ParameterDefinition", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasComment("参数类别");

                    b.Property<byte>("CommandCode")
                        .HasColumnType("INTEGER")
                        .HasComment("关联的命令码");

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasComment("数据类型");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasComment("参数描述");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FormatConfiguration")
                        .HasColumnType("TEXT")
                        .HasComment("格式配置JSON");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsReadOnly")
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("MaxValue")
                        .HasPrecision(18, 6)
                        .HasColumnType("TEXT")
                        .HasComment("最大值");

                    b.Property<decimal?>("MinValue")
                        .HasPrecision(18, 6)
                        .HasColumnType("TEXT")
                        .HasComment("最小值");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("参数名称");

                    b.Property<byte>("ParameterIndex")
                        .HasColumnType("INTEGER")
                        .HasComment("参数索引");

                    b.Property<int>("ParameterTemplateId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasComment("参数单位");

                    b.Property<int>("ValueFormat")
                        .HasColumnType("INTEGER")
                        .HasComment("参数值格式");

                    b.HasKey("Id");

                    b.HasIndex("Category")
                        .HasDatabaseName("IX_ParameterDefinitions_Category");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ParameterDefinitions_IsActive");

                    b.HasIndex("ParameterTemplateId");

                    b.HasIndex("CommandCode", "ParameterIndex")
                        .IsUnique()
                        .HasDatabaseName("IX_ParameterDefinitions_Command_Index");

                    b.ToTable("AM_ParameterDefinitions", (string)null);
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Parameters.ParameterPermission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ParameterDefinitionId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("PermissionLevel")
                        .HasColumnType("INTEGER")
                        .HasComment("权限级别");

                    b.Property<int>("UserRole")
                        .HasColumnType("INTEGER")
                        .HasComment("用户角色");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ParameterPermissions_IsActive");

                    b.HasIndex("ParameterDefinitionId");

                    b.HasIndex("UserRole", "ParameterDefinitionId")
                        .IsUnique()
                        .HasDatabaseName("IX_ParameterPermissions_Role_Parameter");

                    b.ToTable("AM_ParameterPermissions", (string)null);
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Parameters.ParameterTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("ApplicableDeviceType")
                        .HasColumnType("INTEGER")
                        .HasComment("适用的设备类型");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasComment("模板描述");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ModelPattern")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("设备型号匹配模式");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("模板名称");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_ParameterTemplates_Name");

                    b.HasIndex("ApplicableDeviceType", "ModelPattern")
                        .HasDatabaseName("IX_ParameterTemplates_DeviceType_ModelPattern");

                    b.ToTable("AM_ParameterTemplates", (string)null);
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Users.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("邮箱");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset?>("LastLoginAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasComment("密码哈希");

                    b.Property<int>("Role")
                        .HasColumnType("INTEGER")
                        .HasComment("用户角色");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasComment("用户名");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasDatabaseName("IX_Users_Email");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Users_IsActive");

                    b.HasIndex("Role")
                        .HasDatabaseName("IX_Users_Role");

                    b.HasIndex("Username")
                        .IsUnique()
                        .HasDatabaseName("IX_Users_Username");

                    b.ToTable("AM_Users", (string)null);
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Users.UserDevicePermission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("DeviceId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset?>("ExpiresAt")
                        .HasColumnType("TEXT");

                    b.Property<DateTimeOffset>("GrantedAt")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<int>("PermissionLevel")
                        .HasColumnType("INTEGER")
                        .HasComment("权限级别");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("DeviceId");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("IX_UserDevicePermissions_ExpiresAt");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_UserDevicePermissions_IsActive");

                    b.HasIndex("UserId", "DeviceId")
                        .IsUnique()
                        .HasDatabaseName("IX_UserDevicePermissions_User_Device");

                    b.ToTable("AM_UserDevicePermissions", (string)null);
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Monitoring.CommunicationLog", b =>
                {
                    b.HasOne("AirMonitor.Core.Entities.Devices.Device", "Device")
                        .WithMany("CommunicationLogs")
                        .HasForeignKey("DeviceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Device");
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Monitoring.MonitoringData", b =>
                {
                    b.HasOne("AirMonitor.Core.Entities.Devices.Device", "Device")
                        .WithMany("MonitoringData")
                        .HasForeignKey("DeviceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AirMonitor.Core.Entities.Parameters.DeviceParameter", "DeviceParameter")
                        .WithMany("MonitoringData")
                        .HasForeignKey("DeviceParameterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Device");

                    b.Navigation("DeviceParameter");
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Parameters.DeviceParameter", b =>
                {
                    b.HasOne("AirMonitor.Core.Entities.Devices.Device", "Device")
                        .WithMany("DeviceParameters")
                        .HasForeignKey("DeviceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AirMonitor.Core.Entities.Parameters.ParameterDefinition", "ParameterDefinition")
                        .WithMany("DeviceParameters")
                        .HasForeignKey("ParameterDefinitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Device");

                    b.Navigation("ParameterDefinition");
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Parameters.ParameterDefinition", b =>
                {
                    b.HasOne("AirMonitor.Core.Entities.Parameters.ParameterTemplate", "ParameterTemplate")
                        .WithMany("ParameterDefinitions")
                        .HasForeignKey("ParameterTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ParameterTemplate");
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Parameters.ParameterPermission", b =>
                {
                    b.HasOne("AirMonitor.Core.Entities.Parameters.ParameterDefinition", "ParameterDefinition")
                        .WithMany()
                        .HasForeignKey("ParameterDefinitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ParameterDefinition");
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Users.UserDevicePermission", b =>
                {
                    b.HasOne("AirMonitor.Core.Entities.Devices.Device", "Device")
                        .WithMany("UserPermissions")
                        .HasForeignKey("DeviceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AirMonitor.Core.Entities.Users.User", "User")
                        .WithMany("DevicePermissions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Device");

                    b.Navigation("User");
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Devices.Device", b =>
                {
                    b.Navigation("CommunicationLogs");

                    b.Navigation("DeviceParameters");

                    b.Navigation("MonitoringData");

                    b.Navigation("UserPermissions");
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Parameters.DeviceParameter", b =>
                {
                    b.Navigation("MonitoringData");
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Parameters.ParameterDefinition", b =>
                {
                    b.Navigation("DeviceParameters");
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Parameters.ParameterTemplate", b =>
                {
                    b.Navigation("ParameterDefinitions");
                });

            modelBuilder.Entity("AirMonitor.Core.Entities.Users.User", b =>
                {
                    b.Navigation("DevicePermissions");
                });
#pragma warning restore 612, 618
        }
    }
}
