using AirMonitor.Core.Enums;
using AirMonitor.Protocols.Constants;
using AirMonitor.Protocols.Interfaces;
using AirMonitor.Protocols.Models;

namespace AirMonitor.Protocols.Parsers;

/// <summary>
/// 自定义数据格式解析器（定长格式）
/// 格式：7E + 源地址 + 目标地址 + 命令码 + 长度 + 自定义数据... + CRC16
/// 示例：7E F1 00 A1 17 23 60 02 1C 00 1E FF F6 01 9C 00 01 17 18 01 00 CA C3
/// </summary>
public class CustomDataParser : IProtocolParser
{
    /// <summary>
    /// 支持的协议格式
    /// </summary>
    public ProtocolFormat SupportedFormat => ProtocolFormat.CustomData;

    /// <summary>
    /// 解析数据帧
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <returns>解析结果</returns>
    public ParseResult ParseFrame(byte[] rawData)
    {
        var result = new ParseResult
        {
            Format = ProtocolFormat.CustomData,
            RawData = rawData,
            Timestamp = DateTimeOffset.UtcNow
        };

        try
        {
            // 1. 基本长度检查
            if (rawData.Length < ProtocolConstants.MIN_FRAME_LENGTH)
            {
                result.ErrorMessage = "自定义数据格式帧长度不足";
                return result;
            }

            // 2. 帧头检查
            if (rawData[0] != ProtocolConstants.FRAME_HEADER)
            {
                result.ErrorMessage = $"无效的帧头: 0x{rawData[0]:X2}";
                return result;
            }

            // 3. 提取基本信息
            result.SourceAddress = rawData[1];
            result.TargetAddress = rawData[2];
            result.CommandCode = rawData[3];
            result.DataLength = rawData[4];

            // 4. 验证数据长度
            var expectedFrameLength = result.DataLength + 2; // 数据长度 + 头码 + CRC
            if (rawData.Length != expectedFrameLength)
            {
                result.ErrorMessage = $"帧长度不匹配: 期望{expectedFrameLength}, 实际{rawData.Length}";
                return result;
            }

            // 5. CRC校验
            var crcValid = ValidateCRC(rawData, out var calculatedCrc, out var frameCrc);
            result.CrcValid = crcValid;
            result.CalculatedCrc = calculatedCrc;
            result.FrameCrc = frameCrc;

            if (!crcValid)
            {
                result.ErrorMessage = $"CRC校验失败: 计算值=0x{calculatedCrc:X4}, 帧值=0x{frameCrc:X4}";
                return result;
            }

            // 6. 提取自定义数据区
            var customDataLength = result.DataLength - 5; // 总长度减去固定部分(源+目标+命令+长度+CRC2)
            if (customDataLength > 0)
            {
                var customData = new byte[customDataLength];
                Array.Copy(rawData, 5, customData, 0, customDataLength);

                // 根据命令码解析自定义数据
                result.CustomData = ParseCustomData(result.CommandCode, customData);
            }

            result.IsValid = true;
        }
        catch (Exception ex)
        {
            result.ErrorMessage = $"解析异常: {ex.Message}";
            result.IsValid = false;
        }

        return result;
    }

    /// <summary>
    /// 验证数据帧格式
    /// </summary>
    /// <param name="rawData">原始数据</param>
    /// <returns>是否为有效格式</returns>
    public bool CanParse(byte[] rawData)
    {
        if (rawData.Length < ProtocolConstants.MIN_FRAME_LENGTH)
            return false;

        // 检查帧头
        if (rawData[0] != ProtocolConstants.FRAME_HEADER)
            return false;

        // 检查命令码是否为自定义数据相关命令
        var commandCode = rawData[3];
        return commandCode == ProtocolConstants.CMD_SYSTEM_STATUS ||
               commandCode == ProtocolConstants.CMD_DEVICE_INFO ||
               commandCode >= ProtocolConstants.CMD_CONTROL_BASE;
    }

    /// <summary>
    /// 验证CRC校验码
    /// </summary>
    /// <param name="frame">数据帧</param>
    /// <param name="calculatedCrc">计算得到的CRC</param>
    /// <param name="frameCrc">帧中的CRC</param>
    /// <returns>校验是否通过</returns>
    private bool ValidateCRC(byte[] frame, out ushort calculatedCrc, out ushort frameCrc)
    {
        calculatedCrc = 0;
        frameCrc = 0;

        if (frame.Length < ProtocolConstants.MIN_FRAME_LENGTH)
            return false;

        try
        {
            // CRC计算范围：从源地址到数据结束（不包括头码和CRC本身）
            var crcStart = 1; // 从源地址开始
            var crcLength = frame.Length - 3; // 减去头码和2字节CRC

            calculatedCrc = CalculateModbusCRC16(frame, crcStart, crcLength);
            frameCrc = (ushort)((frame[frame.Length - 2] << 8) | frame[frame.Length - 1]);

            return calculatedCrc == frameCrc;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 计算Modbus CRC16校验码
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="start">起始位置</param>
    /// <param name="length">长度</param>
    /// <returns>CRC16值</returns>
    private ushort CalculateModbusCRC16(byte[] data, int start, int length)
    {
        ushort crc = ProtocolConstants.CRC16_INITIAL_VALUE;

        for (int i = start; i < start + length; i++)
        {
            crc ^= data[i];
            for (int j = 0; j < 8; j++)
            {
                if ((crc & 0x0001) != 0)
                {
                    crc >>= 1;
                    crc ^= ProtocolConstants.CRC16_POLYNOMIAL;
                }
                else
                {
                    crc >>= 1;
                }
            }
        }

        return crc;
    }

    /// <summary>
    /// 解析自定义数据
    /// </summary>
    /// <param name="commandCode">命令码</param>
    /// <param name="customData">自定义数据</param>
    /// <returns>解析后的数据字典</returns>
    private Dictionary<string, object> ParseCustomData(byte commandCode, byte[] customData)
    {
        var result = new Dictionary<string, object>();

        try
        {
            switch (commandCode)
            {
                case ProtocolConstants.CMD_SYSTEM_STATUS:
                    ParseSystemStatusData(customData, result);
                    break;

                case ProtocolConstants.CMD_DEVICE_INFO:
                    ParseDeviceInfoData(customData, result);
                    break;

                default:
                    // 对于未知命令，以十六进制字符串形式存储
                    result["RawData"] = Convert.ToHexString(customData);
                    result["DataLength"] = customData.Length;
                    break;
            }
        }
        catch (Exception ex)
        {
            result["ParseError"] = ex.Message;
            result["RawData"] = Convert.ToHexString(customData);
        }

        return result;
    }

    /// <summary>
    /// 解析系统状态数据
    /// 示例数据：23 60 02 1C 00 1E FF F6 01 9C 00 01 17 18 01 00
    /// </summary>
    /// <param name="data">状态数据</param>
    /// <param name="result">结果字典</param>
    private void ParseSystemStatusData(byte[] data, Dictionary<string, object> result)
    {
        if (data.Length < 16)
        {
            result["Error"] = "系统状态数据长度不足";
            return;
        }

        // 解析系统状态位域（示例解析）
        result["SystemStatus"] = data[0]; // 系统状态字节
        result["RunningMode"] = data[1]; // 运行模式
        result["Temperature1"] = (short)((data[2] << 8) | data[3]) / 10.0; // 温度1，精度0.1°C
        result["Temperature2"] = (short)((data[4] << 8) | data[5]) / 10.0; // 温度2，精度0.1°C
        result["Pressure1"] = (ushort)((data[6] << 8) | data[7]) / 100.0; // 压力1，精度0.01MPa
        result["Pressure2"] = (ushort)((data[8] << 8) | data[9]) / 100.0; // 压力2，精度0.01MPa
        result["FlowRate"] = (ushort)((data[10] << 8) | data[11]); // 流量
        result["PowerConsumption"] = (ushort)((data[12] << 8) | data[13]) / 10.0; // 功耗，精度0.1kW
        result["AlarmStatus"] = (ushort)((data[14] << 8) | data[15]); // 告警状态

        // 解析状态位
        var systemStatus = data[0];
        result["IsRunning"] = (systemStatus & 0x01) != 0;
        result["IsHeating"] = (systemStatus & 0x02) != 0;
        result["IsCooling"] = (systemStatus & 0x04) != 0;
        result["HasAlarm"] = (systemStatus & 0x80) != 0;
    }

    /// <summary>
    /// 解析设备信息数据
    /// </summary>
    /// <param name="data">设备信息数据</param>
    /// <param name="result">结果字典</param>
    private void ParseDeviceInfoData(byte[] data, Dictionary<string, object> result)
    {
        if (data.Length < 8)
        {
            result["Error"] = "设备信息数据长度不足";
            return;
        }

        // 解析设备信息（示例解析）
        result["DeviceType"] = data[0]; // 设备类型
        result["HardwareVersion"] = $"{data[1]}.{data[2]}"; // 硬件版本
        result["SoftwareVersion"] = $"{data[3]}.{data[4]}"; // 软件版本
        result["ManufacturerCode"] = (ushort)((data[5] << 8) | data[6]); // 制造商代码
        result["ProductCode"] = data[7]; // 产品代码

        // 如果有更多数据，解析序列号等
        if (data.Length >= 16)
        {
            var serialBytes = new byte[8];
            Array.Copy(data, 8, serialBytes, 0, 8);
            result["SerialNumber"] = Convert.ToHexString(serialBytes);
        }
    }
}
